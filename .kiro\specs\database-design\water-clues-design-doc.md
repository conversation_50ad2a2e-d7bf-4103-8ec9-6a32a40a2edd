# 放水线索相关表结构设计说明文档

## 概述

本文档详细说明了次推应用中放水线索管理系统的数据库表结构设计，包括放水线索表、线索反馈表和线索统计表的设计考虑、字段说明和业务逻辑。

## 设计原则

1. **数据完整性**：通过外键约束确保数据关联的一致性
2. **性能优化**：合理设计索引，支持高频查询场景
3. **扩展性**：预留扩展字段，支持业务功能迭代
4. **统计分析**：内置统计字段，支持数据分析和排序
5. **审核机制**：完整的内容审核流程和状态管理

## 表结构设计详解

### 1. 放水线索表 (ct_water_clues)

#### 业务用途
存储用户提交的放水线索信息，是线索管理系统的核心表。支持线索的发布、审核、统计和推荐功能。

#### 核心字段设计

**基础信息字段**
- `id`: 线索唯一标识，使用bigint确保大数据量支持
- `app_id`: 关联APP信息，支持按APP筛选线索
- `user_id`: 提交用户，用于用户贡献统计和权限控制
- `title`: 线索标题，支持搜索和展示
- `description`: 详细描述，存储线索的具体内容

**放水信息字段**
- `water_amount`: 放水金额，核心业务数据，支持按金额排序
- `water_type`: 放水类型，支持分类筛选（新人福利、活动奖励等）
- `water_time`: 放水时间，支持时间线展示和时间筛选
- `duration_minutes`: 持续时长，帮助用户评估参与时机
- `participant_count`: 参与人数，反映线索热度

**质量评估字段**
- `success_rate`: 成功率，基于用户反馈计算
- `difficulty_level`: 难度等级，帮助用户选择合适的线索
- `verification_status`: 验证状态，确保线索真实性
- `verification_count`: 验证次数，统计验证活跃度
- `success_verification_count`: 成功验证次数，计算成功率

**要求限制字段**
- `device_requirement`: 设备要求，如iOS/Android限制
- `network_requirement`: 网络要求，如WiFi/4G限制
- `region_limitation`: 地区限制，支持地域性活动
- `age_requirement`: 年龄要求，合规性考虑
- `other_requirements`: 其他要求，灵活扩展

**提交信息字段**
- `submitter_device`: 提交人设备信息，用于风控和分析
- `submitter_ip`: 提交IP，支持地理位置分析和风控
- `submitter_location`: 地理位置，支持地域性分析

**审核管理字段**
- `audit_status`: 审核状态，支持内容审核流程
- `audit_user_id`: 审核人，审核责任追溯
- `audit_time`: 审核时间，审核效率统计
- `audit_remark`: 审核备注，审核意见记录

**统计分析字段**
- `view_count`: 查看次数，热度指标
- `like_count`: 点赞次数，用户喜好指标
- `share_count`: 分享次数，传播效果指标
- `comment_count`: 评论次数，互动活跃度
- `collect_count`: 收藏次数，用户价值认可
- `hot_score`: 热度分数，综合排序指标
- `quality_score`: 质量评分，内容质量指标

**推荐管理字段**
- `is_hot`: 热门标记，首页推荐使用
- `is_recommended`: 推荐标记，编辑推荐
- `is_top`: 置顶标记，重要内容突出
- `top_expire_time`: 置顶过期时间，自动管理置顶状态

#### 索引设计考虑

1. **查询性能索引**
   - `idx_water_time`: 支持时间线排序，最常用的查询方式
   - `idx_water_amount`: 支持按金额排序，高收益筛选
   - `idx_hot_score`: 支持热度排序，推荐算法使用

2. **筛选条件索引**
   - `idx_app_id`: 按APP筛选，业务核心查询
   - `idx_water_type`: 按类型筛选，分类浏览
   - `idx_audit_status`: 审核状态筛选，管理后台使用

3. **推荐功能索引**
   - `idx_is_hot`: 热门内容查询
   - `idx_is_recommended`: 推荐内容查询
   - `idx_is_top`: 置顶内容查询

### 2. 线索反馈表 (ct_clue_feedbacks)

#### 业务用途
存储用户对线索的反馈和验证信息，用于线索质量评估、成功率计算和用户体验改进。

#### 核心字段设计

**关联信息字段**
- `clue_id`: 关联线索，建立反馈与线索的关系
- `user_id`: 反馈用户，用于用户行为分析和防刷

**反馈类型字段**
- `feedback_type`: 反馈类型，支持多种反馈场景
  - 1-验证成功：用户成功获得收益
  - 2-验证失败：用户尝试失败
  - 3-举报虚假：用户举报虚假信息
  - 4-补充信息：用户补充额外信息
  - 5-其他：其他类型反馈

**验证结果字段**
- `is_successful`: 是否成功，核心业务指标
- `actual_amount`: 实际获得金额，与预期对比
- `time_spent`: 花费时间，效率评估指标
- `difficulty_rating`: 难度评分，用户主观评价
- `accuracy_rating`: 准确性评分，线索质量评价
- `overall_rating`: 总体评分，综合满意度

**详细信息字段**
- `feedback_content`: 反馈内容，用户详细描述
- `additional_info`: 补充信息，额外有价值信息
- `evidence_files`: 证据文件，JSON格式存储文件列表
- `screenshot_count`: 截图数量，证据完整性指标

**环境信息字段**
- `device_info`: 设备信息，环境因素分析
- `network_info`: 网络环境，影响因素分析
- `location_info`: 地理位置，地域性分析
- `ip_address`: IP地址，风控和地理分析
- `user_agent`: 用户代理，设备和浏览器信息

**隐私保护字段**
- `is_anonymous`: 匿名反馈，保护用户隐私

**质量评估字段**
- `helpful_count`: 有用评价数，反馈质量指标
- `unhelpful_count`: 无用评价数，反馈质量指标

#### 索引设计考虑

1. **关联查询索引**
   - `idx_clue_id`: 查询特定线索的所有反馈
   - `idx_user_id`: 查询特定用户的反馈历史

2. **统计分析索引**
   - `idx_is_successful`: 成功率统计
   - `idx_feedback_type`: 反馈类型分析
   - `idx_overall_rating`: 评分排序

### 3. 线索统计表 (ct_clue_statistics)

#### 业务用途
存储线索的统计数据和分析信息，支持数据分析、排行榜、趋势分析和推荐算法。

#### 核心字段设计

**统计维度字段**
- `clue_id`: 关联线索，统计对象
- `stat_date`: 统计日期，时间维度
- `stat_type`: 统计类型，支持日/周/月统计

**基础统计字段**
- `view_count`: 查看次数，流量指标
- `like_count`: 点赞次数，用户喜好
- `share_count`: 分享次数，传播效果
- `comment_count`: 评论次数，互动活跃度
- `collect_count`: 收藏次数，用户价值认可

**反馈统计字段**
- `feedback_count`: 反馈次数，参与度指标
- `success_feedback_count`: 成功反馈次数，质量指标
- `failed_feedback_count`: 失败反馈次数，风险指标
- `report_count`: 举报次数，内容质量风险

**质量分析字段**
- `success_rate`: 成功率，核心质量指标
- `average_rating`: 平均评分，用户满意度
- `average_amount`: 平均获得金额，收益指标
- `total_amount`: 总获得金额，总价值指标
- `average_time_spent`: 平均花费时间，效率指标

**用户分析字段**
- `unique_user_count`: 独立用户数，覆盖面指标
- `new_user_count`: 新用户数，拉新效果
- `return_user_count`: 回访用户数，留存效果
- `conversion_rate`: 转化率，从查看到反馈的转化

**综合评分字段**
- `engagement_score`: 参与度分数，综合互动指标
- `quality_score`: 质量分数，综合质量评估
- `hot_score`: 热度分数，推荐算法使用
- `trend_score`: 趋势分数，上升/下降趋势

**排名字段**
- `rank_position`: 排名位置，全局排名
- `category_rank`: 分类内排名，分类排名

#### 索引设计考虑

1. **唯一性约束**
   - `uk_clue_date_type`: 防止重复统计，确保数据一致性

2. **查询性能索引**
   - `idx_stat_date`: 时间维度查询
   - `idx_stat_type`: 统计类型筛选

3. **排序功能索引**
   - `idx_success_rate`: 成功率排序
   - `idx_hot_score`: 热度排序
   - `idx_quality_score`: 质量排序
   - `idx_engagement_score`: 参与度排序

## 业务流程支持

### 1. 线索发布流程
1. 用户提交线索信息到`ct_water_clues`表
2. 系统记录提交人设备和IP信息
3. 线索进入待审核状态(`audit_status = 0`)
4. 管理员审核通过后，线索正式发布
5. 系统初始化统计记录到`ct_clue_statistics`表

### 2. 线索验证流程
1. 用户查看线索，`view_count`增加
2. 用户尝试线索，提交反馈到`ct_clue_feedbacks`表
3. 系统更新线索的验证统计字段
4. 触发器自动更新`ct_clue_statistics`表的统计数据
5. 系统重新计算线索的质量评分和热度分数

### 3. 推荐算法支持
1. 基于`hot_score`进行热门推荐
2. 基于`quality_score`进行质量推荐
3. 基于`success_rate`进行高成功率推荐
4. 基于`water_amount`进行高收益推荐
5. 结合用户历史行为进行个性化推荐

### 4. 数据分析支持
1. 通过`ct_clue_statistics`表进行趋势分析
2. 通过反馈数据分析用户行为模式
3. 通过成功率数据优化推荐算法
4. 通过地域数据分析区域性特征

## 性能优化建议

### 1. 查询优化
- 使用复合索引优化多条件查询
- 对热点数据进行缓存
- 使用分页查询避免大数据量查询

### 2. 统计优化
- 使用触发器实时更新统计数据
- 定期批量计算复杂统计指标
- 使用定时任务清理过期统计数据

### 3. 存储优化
- 对大文本字段进行压缩存储
- 使用分区表处理大数据量
- 定期归档历史数据

## 扩展性考虑

### 1. 字段扩展
- 预留扩展字段支持新功能
- JSON字段存储灵活配置信息
- 支持自定义标签和分类

### 2. 功能扩展
- 支持线索订阅和通知
- 支持线索评论和讨论
- 支持线索收藏和分享

### 3. 数据扩展
- 支持多媒体内容存储
- 支持地理位置精确定位
- 支持实时数据同步