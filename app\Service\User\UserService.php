<?php
namespace App\Service\User;

use Exception;
use Carbon\Carbon;
use App\Utils\Tools;
use GuzzleHttp\Client;
use App\Utils\AdSecurity;
use Illuminate\Support\Str;
use App\Service\BaseService;
use App\Models\User\Withdraw;
use App\Exceptions\MyException;
use App\Models\System\AppConfig;
use App\Models\User\UserAdReward;
use App\Models\User\WithdrawThird;
use Illuminate\Support\Facades\DB;
use App\Models\User\UserMaxAdModel;
use App\Models\User\WithdrawReward;
use Illuminate\Support\Facades\Log;
use App\Models\Game\AdRewardKsModel;
use App\Models\System\WithdrawMoney;
use App\Models\Game\AdRewardGdtModel;
use Illuminate\Support\Facades\Cache;
use App\Models\System\AdvertiserModel;
use App\Models\User\User as UserModel;
use App\Models\User\WithdrawRewardThird;
use App\Models\Game\AdRewardGromoreModel;
use App\Models\System\AdRewardCatModel;
use App\Models\System\AdvertisementModel;
use App\Models\User\AdminUser as AdminUserModel;
use App\Models\User\RedPacket as RedPacketModel;
use App\Models\User\UserLogin as UserLoginModel;
use App\Models\User\UserRelation as UserRelationModel;

class UserService extends BaseService{
    //绑定设备用
    public function handleRegOrLoginRecord($user_id,$admin_id,$bind_type,$login_info=[]){
        $user_relation = UserRelationModel::where('user_id',$user_id)->first();
        if($user_relation){
            //存在记录 但是没有绑定设备 第一次绑定  有设备的 就表示在其它设备登录的账号 不需要绑定。
            $up = 0;
            if(empty($user_relation->m_device_id)){
                $user_relation->ip = $login_info['ip'];
                $user_relation->ip_address = $login_info['ip_address'];
                $user_relation->m_type = $login_info['m_type'];
                $user_relation->m_version = $login_info['m_version'];
                $user_relation->m_brand = $login_info['m_brand'];
                $user_relation->m_model = $login_info['m_model'];
                $user_relation->m_network = $login_info['m_network'];
                $user_relation->m_netserver = $login_info['m_netserver'];
                $user_relation->m_simulator = $login_info['m_simulator'];
                $user_relation->m_device_id = $login_info['m_device_id'];
                $user_relation->bind_type = $bind_type;
                $user_relation->update_time = time();
                $up = 1;
            }
            if($user_relation->admin_id != $admin_id){
                $user_relation->admin_id = $admin_id;
                $up = 1;
            }
            if($up == 1){
                $user_relation->save();
            }
        }else{
            //不存在记录 直接绑定
            $user_relation = new UserRelationModel();
            $user_relation->user_id = $user_id;
            $user_relation->admin_id = $admin_id;

            $user_relation->ip = $login_info['ip'];

            $user_relation->ip_address = $login_info['ip_address'];
            $user_relation->m_type = $login_info['m_type'];
            $user_relation->m_version = $login_info['m_version'];
            $user_relation->m_brand = $login_info['m_brand'];
            $user_relation->m_model = $login_info['m_model'];
            $user_relation->m_network = $login_info['m_network'];
            $user_relation->m_netserver = $login_info['m_netserver'];
            $user_relation->m_simulator = $login_info['m_simulator'];
            $user_relation->m_device_id = $login_info['m_device_id'];
            $user_relation->bind_type = $bind_type;
            $user_relation->create_time = time();
            $user_relation->update_time = time();
            $user_relation->save();
        }
    }

    public function userAdInfo($user_id=0){
        if(empty($user_id)){
            return [
                'one_price' => 0,
                'day_price' => 0,
                'month_price' => 0,
                'day_price_pid' => 0,
                'month_price_pid' => 0,
                'day_number' => 0,
                'month_number' => 0,
                'user_money' => 0
            ];
        }
        $user = UserModel::find($user_id);
        if(!$user){
            return [
                'one_price' => 0,
                'day_price' => 0,
                'month_price' => 0,
                'day_price_pid' => 0,
                'month_price_pid' => 0,
                'day_number' => 0,
                'month_number' => 0,
                'user_money' => 0
            ];
        }

        $ad = new AdvertisementModel();

        // 获取今日广告统计
        $today = date('Y-m-d');
        $day_number = $ad->where('user_id', $user_id)->where('can_time', $today)->count();

        $day_price = $ad->where('user_id', $user_id)
                        ->where('can_time', $today)
                        ->selectRaw('COALESCE(SUM(income_user), 0) as total_number')
                        ->value('total_number');
        //今日推广收益
        $day_price_pid = $ad->where('can_time', $today)
                        ->where('pid', $user_id)
                        ->selectRaw('COALESCE(SUM(income_pid), 0) as total_number')
                        ->value('total_number');

        // 获取最近一次广告收益
        $latest = $ad->where('user_id', $user_id)
                    ->orderBy('id', 'desc')
                    ->first();
        $one_price = $latest ? $latest->income_user : 0;

        // 获取本月广告数量
        $month_start = mktime(0, 0, 0, date('m'), 1, date('Y'));
        $month_end = mktime(23, 59, 59, date('m') + 1, 0, date('Y'));

        $month_number = $ad->where('user_id', $user_id)
                            ->whereBetween('create_time', [$month_start, $month_end])
                            ->count();

        $month_price = $ad->where('user_id', $user_id)
                        ->whereBetween('create_time', [$month_start, $month_end])
                        ->selectRaw('COALESCE(SUM(income_user), 0) as total_number')
                        ->value('total_number');

        //本月推广收益
        $month_price_pid = $ad->where('pid', $user_id)
                        ->whereBetween('create_time', [$month_start, $month_end])
                        ->selectRaw('COALESCE(SUM(income_pid), 0) as total_number')
                        ->value('total_number');
        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($user->pid, $system_tg_code_arr)){
            $is_system_tg = 1;
        }
        $user_money = $this->formatDecimal($user->money, 2);
        if($is_system_tg == 0){
            $user_money = "【" . $user_money . "】".$user->phone;
        }else{
            $user_money = "[" . $user_money . "]".$user->phone;
        }

        $day_number = strval((int)$day_number);
        $month_number = strval((int)$month_number).'【'.config('ad.system_name').'】';

        return [
            'one_price' => $this->formatDecimal($one_price, 3),
            'day_price' => $this->formatDecimal($day_price, 2),
            'month_price' => $this->formatDecimal($month_price, 2),
            'day_price_pid' => $this->formatDecimal($day_price_pid, 2),
            'month_price_pid' => $this->formatDecimal($month_price_pid, 2),
            'day_number' => $day_number,
            'month_number' => $month_number,
            'user_money' => $user_money
        ];
    }

    /**
     * 格式化小数，去除末尾多余的0
     * @param float $number 要格式化的数字
     * @param int $decimals 最大小数位数
     * @return string|int 格式化后的数字
     */
    private function formatDecimal($number, $decimals)
    {
        // 使用number_format格式化到指定小数位
        $formatted = number_format((float)$number, $decimals, '.', '');

        // 去掉末尾的0
        $formatted = rtrim($formatted, '0');

        // 如果最后是小数点，去掉小数点
        $formatted = rtrim($formatted, '.');

        return $formatted;
    }

    public function rewardGdt($user){

        /* $router_uri = request()->route()->uri();
        if($router_uri != 'api/rewardGdt_c2pb31ucg6'){
            throw new MyException('访问异常');
        } */


        $data = request()->all();
        $data['user_id'] = $user['id'];
        //systemLog("rewardGdt接收到参数:".json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
        $data['trans_id'] = $data['trans_id'] ?? ($data['transid'] ?? '');

        if(empty($data['trans_id'])){
            throw new MyException('trans_id不能为空');
        }


        // 添加操作频率限制
        $user_id = $data['user_id'];
        $trans_id = $data['trans_id'];
        $key = "gdt_reward_limit:{$user_id}";
        // 尝试设置缓存（仅当键不存在时成功），有效期30秒
        if (!Cache::add($key, now(), 10)) {
            //systemLog 记录详细的参数数据
            $user = UserModel::find($user_id);
            if($user){
                $user->isset = 2;
                $user->save();
            }
            systemLog('USER_ID:'.$user_id.'trans_id:'.$trans_id.' gdt_reward_frequent:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'gdt');
            return [
                "my_money" => 0,
                "user_money" => 0,
                "ad_id" => 0
            ];
        }

        //执行奖励
        $ad_reward_realtime = (int)config('ad.ad_reward_realtime');
        if($ad_reward_realtime == 1){
            return $this->rewardGdtExecute($data);
        }else{
            //非实时处理广告奖励
            return [
                "my_money" => 0,
                "user_money" => $user['money'] ?? 0,
                "ad_id" => 0
            ];
        }
    }

    public function rewardGdtExecute($data){
        $user_id = $data['user_id'];
        $trans_id = $data['trans_id'];
        $money = (int)$data['money'];

        $update_reward_gdt = 0;
        $update_reward_gdt_result = '';

        // 使用事务和状态更新来处理并发
        DB::beginTransaction();
        try {
            // 获取排他锁
            $reward_record = AdRewardGdtModel::where('trans_id', $trans_id)
                            ->lockForUpdate()
                            ->first();
            if(!$reward_record) {
                throw new MyException('trans_id:'.$trans_id.'不存在');
            }

            if($reward_record->status != 0) {
                throw new MyException('trans_id:'.$trans_id.'奖励已被处理,请勿重复领取');
            }

            // 更新状态
            $reward_record->status = 2;
            $reward_record->save();

            $reward_record = $reward_record->toArray();

            if($reward_record['status'] != 2){
                throw new MyException('trans_id:'.$trans_id.'该奖励已操作');
            }

            $data['reward_id'] = $reward_record['id'];

            $user = UserModel::find($user_id);
            if(!$user){
                $update_reward_gdt_result = '用户不存在';
                $update_reward_gdt = 1;
                throw new MyException('trans_id:'.$trans_id.'用户不存在');
            }

            $user = $user->toArray();

            if($user['isset'] != 1){
                $update_reward_gdt_result = '用户已禁用';
                $update_reward_gdt = 1;
                throw new MyException('trans_id:'.$trans_id.'用户已禁用');
            }

            $res = $this->rewardUserPacket($data);
            if(isset($res['ad_id'])){
                DB::table('ad_reward_relation')->insert([
                    'ad_id'         => $res['ad_id'],
                    'type'          => 'gdt',
                    'reward_id'     => $data['reward_id'] ?? '',
                    'trans_id'      => $trans_id,
                    'create_time'   => time(),
                    'create_time_f' => date('Y-m-d H:i:s',time())
                ]);
                AdRewardGdtModel::where('id',$reward_record['id'])
                ->update([
                    'status' => 1,
                    'receive_over' => 1,
                    'result' => '奖励验证成功',
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            DB::commit();
            return $res;
        } catch(\Exception $e) {
            DB::rollBack();
            if($update_reward_gdt == 1){
                AdRewardGdtModel::where('trans_id',$trans_id)
                ->update([
                    'status' => 3,
                    'receive_over' => 1,
                    'result' => $update_reward_gdt_result,
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            throw new MyException($e->getMessage());
        }
    }

    public function rewardKs($user){
        $data = request()->all();
        $data['user_id'] = $user['id'];
        //systemLog("rewardKs接收到参数:".json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        $data['trans_id'] = $data['trans_id'] ?? ($data['transId'] ?? '');

        if(empty($data['trans_id'])){
            throw new MyException('trans_id不能为空');
        }

        //执行奖励
        $ad_reward_realtime = (int)config('ad.ad_reward_realtime');
        if($ad_reward_realtime == 1){
            return $this->rewardKsExecute($data);
        }else{
            //非实时处理广告奖励
            return [
                "my_money" => 0,
                "user_money" => $user['money'] ?? 0,
                "ad_id" => 0
            ];
        }
    }

    public function rewardKsExecute($data){
        $user_id = $data['user_id'];
        $trans_id = $data['trans_id'];
        $money = (int)$data['money'];

        $update_reward_ks = 0;
        $update_reward_ks_result = '';

        // 使用事务和状态更新来处理并发
        DB::beginTransaction();
        try {
            // 获取排他锁
            $reward_record = AdRewardKsModel::where('trans_id', $trans_id)
                            ->lockForUpdate()
                            ->first();
            if(!$reward_record) {
                throw new MyException('trans_id:'.$trans_id.'不存在');
            }

            if($reward_record->status != 0) {
                throw new MyException('trans_id:'.$trans_id.'奖励已被处理,请勿重复领取');
            }

            // 更新状态
            $reward_record->status = 2;
            $reward_record->save();

            $reward_record = $reward_record->toArray();

            if($money != (int)$reward_record['reward_amount']){
                $update_reward_ks_result = '奖励金额错误';
                $update_reward_ks = 1;
                throw new MyException('trans_id:'.$trans_id.'奖励金额错误');
            }

            if($reward_record['status'] != 2){
                throw new MyException('trans_id:'.$trans_id.'该奖励已操作');
            }

            $data['reward_id'] = $reward_record['id'];

            $user = UserModel::find($user_id);
            if(!$user){
                $update_reward_ks_result = '用户不存在';
                $update_reward_ks = 1;
                throw new MyException('trans_id:'.$trans_id.'用户不存在');
            }

            $user = $user->toArray();

            if($user['isset'] != 1){
                $update_reward_ks_result = '用户已禁用';
                $update_reward_ks = 1;
                throw new MyException('trans_id:'.$trans_id.'用户已禁用');
            }

            $res = $this->rewardUserPacket($data);
            if(isset($res['ad_id'])){
                DB::table('ad_reward_relation')->insert([
                    'ad_id'         => $res['ad_id'],
                    'type'          => 'ks',
                    'reward_id'     => $data['reward_id'] ?? '',
                    'trans_id'      => $trans_id,
                    'create_time'   => time(),
                    'create_time_f' => date('Y-m-d H:i:s',time())
                ]);
                AdRewardKsModel::where('id',$reward_record['id'])
                ->update([
                    'status' => 1,
                    'receive_over' => 1,
                    'result' => '奖励验证成功',
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            DB::commit();
            return $res;
        } catch(\Exception $e) {
            DB::rollBack();
            if($update_reward_ks == 1){
                AdRewardKsModel::where('trans_id',$trans_id)
                ->update([
                    'status' => 3,
                    'receive_over' => 1,
                    'result' => $update_reward_ks_result,
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            throw new MyException($e->getMessage());
        }
    }

    public function rewardGromore($user){

        /* $router_uri = request()->route()->uri();
        if($router_uri != 'api/rewardGromore_a2cpb7byce'){
            throw new MyException('访问异常');
        } */

        $data = request()->all();
        $data['user_id'] = $user['id'];
        //systemLog("rewardGromore接收到参数:".json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
        if(empty($data['trans_id'])){
            throw new MyException('trans_id不能为空');
        }


        $user_id = $data['user_id'];
        $trans_id = $data['trans_id'];
        // 使用用户ID和trans_id作为缓存键的一部分，区分不同用户的不同回调
        $key = "gromore_reward_limit:{$user_id}";
        // 尝试设置缓存（仅当键不存在时成功），有效期30秒
        if (!Cache::add($key, now(), 10)) {
            //systemLog 记录详细的参数数据
            systemLog('USER_ID:'.$user_id.'trans_id:'.$trans_id.' gromore_reward_frequent:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'gromore');
            // 如果设置失败，说明操作过于频繁，直接返回空数组或者特定错误结构
            // 注意：此方法通常抛出异常，但为了参考回调方法的限制方式（返回数组），这里也返回数组
            $user = UserModel::find($user_id);
            if($user){
                $user->isset = 2;
                $user->save();
            }
            return [
                "my_money" => 0, // 或其他表示错误的字段
                "user_money" => $user['money'] ?? 0, // 保留原有字段结构
                "ad_id" => 0,
                "error_msg" => "系统已对本次访问的详细数据记录包括不限于IP 设备硬件 网络信息 账号信息等数据，已自动上报网警相关部门，请勿重复操作" // 添加错误信息
            ];
        }

        //执行奖励
        $ad_reward_realtime = (int)config('ad.ad_reward_realtime');
        if($ad_reward_realtime == 1){
            return $this->rewardGromoreExecute($data);
        }else{
            //非实时处理广告奖励
            return [
                "my_money" => 0,
                "user_money" => $user['money'] ?? 0,
                "ad_id" => 0
            ];
        }
    }

    public function rewardGromoreExecute($data){
        $user_id    = $data['user_id'];
        $trans_id   = $data['trans_id'];
        $money      = (int)$data['money'];
        $advertiser = $data['advertiser'];

        $update_reward_gromore = 0;
        $update_reward_gromore_result = '';

        // 使用事务和状态更新来处理并发
        DB::beginTransaction();
        try {
            // 获取排他锁
            $reward_record = AdRewardGromoreModel::where('trans_id', $trans_id)
                            ->lockForUpdate()
                            ->first();
            if(!$reward_record) {
                throw new MyException('trans_id:'.$trans_id.'不存在');
            }

            if($reward_record->status != 0) {
                throw new MyException('trans_id:'.$trans_id.'奖励已被处理,请勿重复领取');
            }
            // 更新状态
            $reward_record->status = 2;
            $reward_record->save();

            $reward_record = $reward_record->toArray();

            if($money != (int)$reward_record['reward_amount']){
                $update_reward_gromore_result = '奖励金额错误';
                $update_reward_gromore = 1;
                throw new MyException('trans_id:'.$trans_id.'奖励金额错误');
            }

            if($reward_record['status'] != 2){
                throw new MyException('trans_id:'.$trans_id.'该奖励已操作');
            }

            $data['reward_id'] = $reward_record['id'];

            $user = UserModel::find($user_id);
            if(!$user){
                $update_reward_gromore_result = '用户不存在';
                $update_reward_gromore = 1;
                throw new MyException('trans_id:'.$trans_id.'用户不存在');
            }

            $user = $user->toArray();

            if($user['isset'] != 1){
                $update_reward_gromore_result = '用户已禁用';
                $update_reward_gromore = 1;
                throw new MyException('trans_id:'.$trans_id.'用户已禁用');
            }
            $res = $this->rewardUserPacket($data);
            if(isset($res['ad_id'])){
                DB::table('ad_reward_relation')->insert([
                    'ad_id'         => $res['ad_id'],
                    'type'          => 'gromore',
                    'reward_id'     => $data['reward_id'] ?? '',
                    'trans_id'      => $trans_id,
                    'create_time'   => time(),
                    'create_time_f' => date('Y-m-d H:i:s',time())
                ]);
                AdRewardGromoreModel::where('id',$reward_record['id'])
                ->update([
                    'status' => 1,
                    'receive_over' => 1,
                    'result' => '奖励验证成功',
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            DB::commit();
            return $res;
        } catch(\Exception $e) {
            DB::rollBack();
            if($update_reward_gromore == 1){
                AdRewardGromoreModel::where('trans_id',$trans_id)
                ->update([
                    'status' => 3,
                    'receive_over' => 1,
                    'result' => $update_reward_gromore_result,
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            throw new MyException($e->getMessage());
        }
    }
    /* 奖励用户红包 */
    public function rewardUserPacket($data){
        $user_id = $data['user_id'];
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }

        if($user->isset != 1){
            throw new MyException('用户已禁用');
        }

        // 获取请求参数
        $moneys = $data['money']; // 价格
        $advertiser = $data['advertiser']; // 广告商

        $ip = Tools::getIp();
        $m_type = $data['m_type'] ?? 1; // 机型
        $m_version = $data['m_version'] ?? ''; // 操作系统版本
        $m_brand = $data['m_brand'] ?? ''; // 品牌
        $m_model = $data['m_model'] ?? ''; // 型号
        $m_device_id = $data['m_device_id'] ?? ''; // 设备编码
        $m_network = $data['m_network'] ?? 0; // 网络类型
        $m_netserver = $data['m_netserver'] ?? 0; // 网络运营商
        $m_simulator = $data['m_simulator'] ?? 0; // 模拟器

        // 验证广告商
        $ads = AdvertiserModel::where(['as' => $advertiser])->first();
        if(empty($ads)){
            throw new MyException('广告商不正确');
        }
        $advertiser = $ads->id;

        if(empty($moneys) || empty($advertiser)){
            throw new MyException('参数不能为空');
        }
        // 转换金额为元
        $value = ($moneys/1000)/100;
        $multiplier = pow(10, 3);
        $moneys = floor($value * $multiplier) / $multiplier;


        $change_user_ad_number = 0;
        $change_user_ad_number_num = 40;

        //动态调整非内部账号的观看广告次数
        if($user->inner_account == 0){
            // 如果广告金额小于0.01元 并且观看广告次数大于10次 则调整观看广告次数为10次
            if($moneys <=0.01 && $user->ad_number > 40){
                $change_user_ad_number = 1;
                $change_user_ad_number_num = 40;
            }
            // 如果广告金额大于0.01元 并且观看广告次数小于10次 则调整观看广告次数为60次
            if($moneys >0.01 && $user->ad_number <= 40){
                $change_user_ad_number = 1;
                $change_user_ad_number_num = 60;
            }
        }
        //小号强制不修改广告条数
        $current_user_cheat = (int)$user->is_cheat;
        if($current_user_cheat == 1){
            $change_user_ad_number = 0;
        }

        //是否是第一次触发广告最大值
        $first_max_ad = 1;
        $change_user_commission = 0;
        $change_user_commission_num_no_agent = 30;
        $change_user_commission_num_with_agent = 30;

        if($user->inner_account == 0){
            if($moneys < 0.8){
                if((int)$user->pid <=0){
                    if($user->commission !=20){
                        $change_user_commission = 1;
                        $change_user_commission_num_no_agent = 20;
                        $user->commission = 20;
                    }
                }else{
                    if($user->user_commission !=50){
                        $change_user_commission = 1;
                        $change_user_commission_num_with_agent = 50;
                        $user->user_commission = 50;
                    }
                }
                $first_max_ad = 0;
            }

            if($moneys >= 0.8){
                $user_max_ad = UserMaxAdModel::where('user_id',$user_id)->first();
                if(!$user_max_ad){
                    //兼容历史数据 用户收益大于0.3元 则认为已经触发过广告最大值
                    $ad_income = AdvertisementModel::query()->where('user_id',$user_id)->max('income_user');
                    if($ad_income && (float)$ad_income >= 0.3){
                        $first_max_ad = 0;
                        UserMaxAdModel::create([
                            'user_id' => $user_id,
                            'ad_id' => 0,
                            'money' => $ad_income,
                            'ad_time' => time(),
                            'ad_time_f' => date('Y-m-d H:i:s',time())
                        ]);
                    }else{
                        $first_max_ad = 1;
                    }
                }else{
                    $first_max_ad = 0;
                }
            }else{
                $first_max_ad = 0;
            }

            if($moneys >= 0.8 && $moneys <= 1.5){
                if((int)$user->pid <=0){
                    if($user->commission !=13){
                        $change_user_commission = 1;
                        $change_user_commission_num_no_agent = 13;
                    }
                    if($first_max_ad == 0){
                        $user->commission = 13;
                    }
                }else{
                    if($user->user_commission !=25){
                        $change_user_commission = 1;
                        $change_user_commission_num_with_agent = 25;
                    }
                    if($first_max_ad == 0){
                        $user->user_commission = 25;
                    }
                }
            }

            if($moneys > 1.5 && $moneys <= 2){
                if((int)$user->pid <=0){
                    if($user->commission !=9){
                        $change_user_commission = 1;
                        $change_user_commission_num_no_agent = 9;
                    }
                    if($first_max_ad == 0){
                        $user->commission = 9;
                    }
                }else{
                    if($user->user_commission !=18){
                        $change_user_commission = 1;
                        $change_user_commission_num_with_agent = 18;
                    }
                    if($first_max_ad == 0){
                        $user->user_commission = 18;
                    }
                }
            }

            if($moneys > 2){
                if((int)$user->pid <=0){
                    if($user->commission !=5){
                        $change_user_commission = 1;
                        $change_user_commission_num_no_agent = 5;
                    }
                    if($first_max_ad == 0){
                        $user->commission = 5;
                    }
                }else{
                    if($user->user_commission !=12){
                        $change_user_commission = 1;
                        $change_user_commission_num_with_agent = 12;
                    }
                    if($first_max_ad == 0){
                        $user->user_commission = 12;
                    }
                }
            }
        }else{
            $first_max_ad = 0;
        }




        if($moneys <= 0){
            systemLog('广告金额'.$moneys.'值小于0.001');
            return [];
        }

        //系统设置的单次会员最高佣金 单位 元
        $max_money_user = (float)getAppConfig('money','max_money_user');
        if($max_money_user<=0){
            //没有设置就是0.5
            $max_money_user = 0.5;
        }
        if($max_money_user>1){
            $max_money_user = 1;//用户奖励强制最高1元
        }

        $current_user_cheat = (int)$user->is_cheat;
        $current_user_pid_cheat = 0;
        $user_pid = null;
        if($user->pid>0){
            $user_pid = UserModel::find($user->pid);
            if($user_pid){
                $current_user_pid_cheat = (int)$user_pid->is_cheat;
            }
        }
        // 小号用户最高佣金
        $cheat_user = number_format((float)getAppConfig('money','cheat_user'),2);
        // 小号代理最高佣金
        $cheat_admin = number_format((float)getAppConfig('money','cheat_admin'),2);

        $fk = 0;
        $execute_max_money_user = 0;
        $execute_max_money_agent = 0;

        $reward_log = "";
        $reward_log .= "\r\n用户ID：".$user_id.",手机号码：".$user->phone.($user->inner_account == 1 ? ",内部账号" : "");
        $reward_log .= ",\r\n广告金额：".$moneys;

        // 开启数据库事务
        DB::beginTransaction();
        try {
            //会员没有代理 处理相关奖励
            $is_cheat = (int)$user->is_cheat;
            //查询该会员有没有作弊 同一台设备登录该账号和上级账号
            $user_relation = UserRelationModel::where('user_id',$user->id)->first();
            if($user_relation){
                $is_cheat = (int)$user_relation->is_cheat;
            }
            if($user->inner_account == 1){
                //内部账号 不处理作弊
                $is_cheat = 0;
            }

            if((int)$user->pid <=0){
                $reward_log .= ",没有代理";
                $system_commission = (int)getAppConfig('money','user');
                $user_commission = (int)$user->commission;

                if($user->inner_account == 0){
                    if($system_commission > 0 && $user_commission > 0) {
                        $bl = min($system_commission, $user_commission);
                        $reward_log .= ",\r\n取最小值分佣比例：".$bl."%";
                    } else {
                        $bl = $user_commission > 0 ? $user_commission : $system_commission;
                        $reward_log .= ",\r\n只配了一个分佣比例：".$bl."%";
                    }
                    if( $is_cheat == 1 && $bl > 25){
                        //作弊 强制最高25%
                        $bl = 25;
                    }
                }else{
                    //内部账号 按配置直接分配
                    $bl = $user_commission > 0 ? $user_commission : $system_commission;
                    $reward_log .= ",分佣比例：".$bl."%";
                }

                $bl = $bl * 0.01;
                $user_money = number_format($moneys * $bl, 3);
                $reward_log .= ",原始分成：".$user_money;

                if($user->inner_account == 0){
                    if($user->reward_user == 0){
                        //该用户不允许奖励
                        $user_money = 0;
                        $reward_log .= ",不允许奖励";
                    }else{
                        // 检查单次会员最高佣金
                        if($user_money>$max_money_user){
                            $user_money = $max_money_user;
                            $reward_log .= ",\r\n触发用户最高佣金：".$max_money_user;
                            $execute_max_money_user = 1;
                            if($user_money >= 0.01){
                                //随机减去0.001-0.099之间的值确保有小数
                                $random_reduce = Tools::getFloatRandom(0.009);
                                $user_money = number_format($user_money - $random_reduce, 3);
                            }
                            $reward_log .= ",减去随机值：".$random_reduce;
                        }
                        // 小号用户最高佣金

                        if($current_user_cheat == 1){
                            //当前用户是小号
                            $reward_log .= ",\r\n当前用户是小号";
                            if($cheat_user > 0){
                                /* $reward_log .= ",小号最高佣金：".$cheat_user;
                                if($user_money > $cheat_user){
                                    $user_money = Tools::getFloatRandom($cheat_user);
                                    $reward_log .= ",触发小号最高佣金：".$cheat_user;
                                    $execute_max_money_user = 1;
                                } */

                                $reward_log .= ",小号固定佣金：0.05";
                                $user_money = 0.05;

                            }else{
                                $user_money = 0;
                                $reward_log .= ",小号最高佣金为0";
                            }
                        }
                    }
                }

                //红包记录
                $data = [];

                if($user_money > 0) {
                    $reward_log .= ",\r\n用户分成：".$user_money;
                    $data[] = [
                        'user_id' => $user_id,
                        'money' => $user_money,
                        'first_money' => $moneys,
                        'pid' => 0,
                        'identity' => $user->identity,
                        'from_user_id' => 0,//用户ID
                        'ad_id' => 0,//广告记录ID
                        'state' => 1,
                        'type' => 1,//1广告红包  2代理奖励
                        'invite_level' => 0,//邀请等级
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }
                //广告记录
                if($moneys){
                    $arr = [
                        'user_id' => $user_id,
                        'advertiser_id' => $advertiser,
                        'income' => $moneys,//广告金额
                        'can_time' => date('Y-m-d'),
                        'income_user' => $user_money,//用户收益
                        'pid' => 0,
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }

                if($change_user_commission == 1){
                    $reward_log .= ",\r\n触发调整用户分成比例：".$change_user_commission_num_no_agent."%";
                }

                if($change_user_commission == 1){
                    //调整用户分成
                    UserModel::where('id',$user_id)
                    ->update([
                        'commission' => $change_user_commission_num_no_agent
                    ]);
                }

            }else{
                //会员有代理
                $max_money_agent = (float)getAppConfig('money','max_money_agent');
                if($max_money_agent<=0){
                    //没有设置就是0.5
                    $max_money_agent = 0.5;
                }
                if($max_money_agent>0.5){
                    $max_money_agent = 0.5;//代理奖励强制最高0.5元
                }

                $reward_log .= ",有代理";
                $reward_log .= ",代理ID：".$user->pid;

                //取上级账号的代理分成
                if(!$user_pid){
                    $user_pid = UserModel::find($user->pid);
                }
                // 计算代理分成
                $system_commission = (int)getAppConfig('money','admin');
                $user_commission = (int)$user_pid->admin_commission;

                //这里的比例不能按是否内部账号判断 因为是要给用户分成的 取的是上级账号的代理分成
                if($system_commission > 0 && $user_commission > 0) {
                    $bl = min($system_commission, $user_commission);
                } else {
                    $bl = $user_commission > 0 ? $user_commission : $system_commission;
                }

                $reward_log .= ",\r\n代理分佣比例：".$bl."%";

                $bl = $bl * 0.01;
                $pid_money = number_format($moneys * $bl, 3);
                // 检查单次会员最高佣金
                // 计算用户分成
                $yh = empty($user->user_commission) ? getAppConfig('money','user_invite') : $user->user_commission;
                $reward_log .= ",用户分成比例：".$yh."%";
                $yh = $yh * 0.01;
                $user_money = number_format($pid_money * $yh, 3);
                $reward_log .= ",\r\n原始分成：".$user_money;
                if($user->inner_account == 0){
                    if($user->reward_user == 0){
                        $user_money = 0;
                        $reward_log .= ",不允许奖励";
                    }else{
                        /* if($user->is_cheat == 1){
                            $reward_log .= ",\r\n当前用户是小号";
                            if($cheat_user > 0){
                                $reward_log .= ",小号最高佣金：".$cheat_user;
                                if($user_money > $cheat_user){
                                    $execute_max_money_user = 1;
                                    $user_money = Tools::getFloatRandom($cheat_user);
                                    $reward_log .= ",触发小号最高佣金：".$cheat_user;
                                }
                            }else{
                                $user_money = 0;
                                $reward_log .= ",小号最高佣金为0";
                            }
                        } */
                        if($user_money>$max_money_user){
                            $reward_log .= ",触发用户最高佣金：".$max_money_user;
                            $execute_max_money_user = 1;
                            $user_money = $max_money_user;
                            if($user_money >= 0.1){
                                //随机减去0.001-0.099之间的值确保有小数
                                $random_reduce = Tools::getFloatRandom(0.009);
                                $user_money = number_format($user_money - $random_reduce, 3);
                                $reward_log .= ",减去随机值：".$random_reduce;
                            }
                        }
                    }
                }

                if($user->is_cheat == 1){
                    $reward_log .= ",\r\n当前用户是小号";
                    $reward_log .= ",小号固定佣金：0.05";
                    $user_money = 0.05;

                }



                if($user->is_cheat == 0){

                    $money_pid = number_format($pid_money - $user_money, 3);
                    if($user_pid->inner_account == 0){
                        //如果推荐人是作弊号 或者当前用户是作弊号 最多给0.05
                        /* if($user_pid->is_cheat == 1 || $user->is_cheat == 1){
                            $reward_log .= ",\r\n推荐人是小号 或者当前用户是小号";
                            if($cheat_admin > 0){
                                if($money_pid > $cheat_admin){
                                    $money_pid = Tools::getFloatRandom($cheat_admin);
                                    $reward_log .= ",触发小号代理最高佣金：".$cheat_admin;
                                    $execute_max_money_agent = 1;
                                }
                            }else{
                                $money_pid = 0;
                                $reward_log .= ",小号代理最高佣金为0";
                            }
                        } */

                        if($money_pid>$max_money_agent){
                            $money_pid = $max_money_agent;
                            $reward_log .= ",触发代理最高佣金：".$max_money_agent;
                            $execute_max_money_agent = 1;
                            if($money_pid >= 0.1){
                                //随机减去0.001-0.099之间的值确保有小数
                                $random_reduce = Tools::getFloatRandom(0.009);

                                $money_pid = number_format($money_pid - $random_reduce, 3);
                                $reward_log .= ",减去随机值：".$random_reduce;
                            }

                        }
                        //看广告成员设置的不允许给代理奖励 或者 上级用户设置的不允许给奖励
                        if($user->reward_admin == 0 || $user_pid->reward_user == 0){
                            if($user->reward_admin == 0){
                                $reward_log .= ",\r\n看广告成员设置的不允许给代理奖励";
                            }
                            if($user_pid->reward_user == 0){
                                $reward_log .= ",\r\n上级用户设置的不允许给奖励";
                            }

                            $money_pid = 0;
                        }
                    }


                    if($money_pid > 0 && $user_money > 0) {
                        //都大于0时 如果 $money_pid 不等于 $user_money 则需要强制把 $money_pid 设置为 $user_money
                        if($money_pid != $user_money) {
                            $min_value = min($money_pid, $user_money);
                            $reward_log .= ",\r\n检测到代理分成(".$money_pid.")与用户分成(".$user_money.")不一致,强制设置为最小值:".$min_value;
                            $money_pid = $min_value;
                            $user_money = $min_value;
                        }
                    }
                    $money_pid_2 = 0;
                    if($money_pid >= 0.002) {
                        $ad_reward_cat = AdRewardCatModel::query()->where('id',1)->first();  
                        $level_1 = 50;
                        $level_2 = 50;
                        if($ad_reward_cat) {
                            $level_1 = (int)$ad_reward_cat->level_1;
                            $level_2 = (int)$ad_reward_cat->level_2;
                        }

                        // 根据level_1和level_2的百分比分配代理分成
                        $money_pid_1 = number_format($money_pid * $level_1 / 100, 3);
                        $money_pid_2 = number_format($money_pid * $level_2 / 100, 3);
                        
                        // 第一份给当前用户的代理
                        $money_pid = $money_pid_1;
                        
                        $reward_log .= ",\r\n代理分成按比例分配,首推{$level_1}%:{$money_pid_1},次推{$level_2}%:{$money_pid_2}";
                    }
                    //二级分成    
                    $pid_2 = $user->pid_2;
                    if($pid_2 > 0 && $money_pid_2 > 0){
                        $user_2 = UserModel::find($pid_2);
                        //没有禁用 不是小号 允许奖励
                        if($user_2 && $user_2->isset == 1 && $user_2->is_cheat == 0 && $user_2->reward_user == 1){
                            $reward_log .= ",\r\n二级分成:".$money_pid_2;
                        }else{
                            $money_pid_2 = 0;
                            $reward_log .= ",\r\n二级分成用户不符合领取奖励条件,取消二级分成";
                        }
                    }


                }else{
                    $money_pid = 0.025;
                    $money_pid_2 = 0.025;
                    $reward_log .= ",\r\n当前用户是小号,代理总分成固定为0.05,首推0.025,次推0.025";
                }




                $data = [];

                if($user_money > 0) {
                    $reward_log .= ",\r\n用户分成：".$user_money;

                    $data[] = [
                        'user_id' => $user_id,
                        'money' => $user_money,
                        'pid' => $user->pid,
                        'identity' => $user->identity,
                        'first_money' => $moneys,
                        'from_user_id' => 0,//用户ID
                        'ad_id' => 0,//广告记录ID
                        'state' => 1,
                        'type' => 1,//1广告红包  2代理奖励
                        'invite_level' => 0,//邀请等级
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }
                if($money_pid > 0 && $user->pid > 0) {
                    $reward_log .= ",\r\n一级代理分成：".$money_pid;
                    $data[] = [
                        'user_id' => $user->pid,
                        'money' => $money_pid,//代理分成金额
                        'first_money' => $moneys,//用户和代理总的分成
                        'pid' => $user->pid_2,
                        'from_user_id' => $user_id,//用户ID
                        'ad_id' => 0,//广告记录ID
                        'identity' => 2,
                        'state' => 1,
                        'type' => 2,//1广告红包  2代理奖励
                        'invite_level' => 1,//邀请等级
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }

                if($money_pid_2 > 0 && $user->pid_2 > 0) {
                    $reward_log .= ",\r\n二级代理分成：".$money_pid_2;
                    $data[] = [
                        'user_id' => $user->pid_2,
                        'money' => $money_pid_2,//代理分成金额
                        'first_money' => $moneys,//用户和代理总的分成
                        'pid' => 0,//这一层级不再记录父级ID 没有意义
                        'from_user_id' => $user_id,//用户ID
                        'ad_id' => 0,//广告记录ID
                        'identity' => 2,
                        'state' => 1,
                        'type' => 2,//1广告红包  2代理奖励
                        'invite_level' => 2,//邀请等级
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }

                if($moneys > 0){
                    $arr = [
                        'user_id' => $user_id,
                        'advertiser_id' => $advertiser,
                        'income' => $moneys,
                        'can_time' => date('Y-m-d'),
                        'income_user' => $user_money,
                        'pid' => 0,
                        'income_pid' => $money_pid + $money_pid_2,
                        'pid_1' => $user->pid,
                        'income_pid_1' => $money_pid,
                        'pid_2' => $user->pid_2,
                        'income_pid_2' => $money_pid_2,
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }

                if($change_user_commission == 1){
                    //调整用户分成
                    UserModel::where('id',$user_id)
                    ->update([
                        'user_commission' => $change_user_commission_num_with_agent
                    ]);
                }


                if($change_user_commission == 1){
                    $reward_log .= ",\r\n触发调整用户分成比例：".$change_user_commission_num_with_agent."%";
                }

            }

            if($change_user_ad_number == 1){
                $reward_log .= ",\r\n触发调整用户广告次数：".$change_user_ad_number_num;
                //调整用户广告次数
                UserModel::where('id',$user_id)
                ->update([
                    'ad_number' => $change_user_ad_number_num
                ]);
            }

            $ad = null;
            // 创建广告观看记录
            //$moneys = $this->convert($moneys);
            if(isset($arr) && count($arr) > 0){
                if(!isset($arr['income_pid'])){
                    $arr['income_pid'] = 0;
                }
                $arr['profit'] = number_format($moneys - $arr['income_user'] - $arr['income_pid'], 3);
                $reward_log .= ",\r\n利润：".$arr['profit'];
                $arr['ip'] = $ip;
                $arr['m_type'] = $m_type;
                $arr['m_version'] = $m_version;
                $arr['m_brand'] = $m_brand;

                $arr['m_model'] = $m_model;

                $arr['m_device_id'] = $m_device_id;
                $arr['m_network'] = $m_network;
                $arr['m_netserver'] = $m_netserver;
                $arr['m_simulator'] = $m_simulator;
                $ad = AdvertisementModel::create($arr);
            }

            if(isset($data) && count($data) > 0){
                // 创建红包记录并更新用户余额
                foreach($data as $v){
                    $v['ad_id'] = $ad->id; // 设置广告ID
                    $res = RedPacketModel::create($v);
                    $user_only = UserModel::find($v['user_id']);
                    if($user_only) { // 添加这个判断
                        $user_only->money += $res->money;
                        $user_only->save();
                    }
                }
            }
            if($ad && $ad->id){
                if($user->is_cheat==1 || $execute_max_money_user==1 || $execute_max_money_agent==1){
                    $fk = 1;
                }
                DB::table('ad_log')->insert([
                    'ad_id' => $ad->id,
                    'user_id' => $user_id,
                    'user_pid' => $user->pid,
                    'user_pid_2' => $user->pid_2,
                    'inner_account' => $user->inner_account,
                    'is_cheat' => $user->is_cheat,
                    'fk' => $fk,
                    'reward_user' => $user->reward_user,
                    'reward_admin' => $user->reward_admin,
                    'max_money_user' => $execute_max_money_user,
                    'max_money_agent' => $execute_max_money_agent,
                    'ad_log' => $reward_log,
                    'create_time' => time()
                ]);

                //记录该用户第一次触发广告最大值
                if($first_max_ad == 1){
                    UserMaxAdModel::create([
                        'user_id' => $user_id,
                        'ad_id' => $ad->id,
                        'money' => $moneys,
                        'ad_time' => time(),
                        'ad_time_f' => date('Y-m-d H:i:s',time())
                    ]);
                }
            }

            DB::commit();
            //Log::channel('daily')->info($reward_log);
            return [
                'my_money' => $user_money,
                'user_money' => number_format($user->money + $user_money, 3),
                'ad_id' => $ad->id
            ];
        } catch(\Exception $e) {
            DB::rollBack();
            throw new MyException($e->getMessage());
        }
    }

    //文档地址 https://www.csjplatform.com/supportcenter/26240
    //广告奖励回调地址 https://www.gromore.com/reward/callback?user_id=1234&trans_id=qwerfdas&reward_amount=100&reward_name=rmb&prime_rit=900000000&mediation_rit=800000000&sign=sign:ebdc5645bc6245819fec2324789a363865272926cd0f8e4e88a993bd7fe3ba81&extra=anything_media_wan
    public function rewardGromoreCallback(){

        $data = request()->all();
        //systemLog('gromore_reward_callback:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'gromore');
        if(empty($data['ecpm']) || empty($data['user_id']) || empty($data['trans_id']) || empty($data['reward_amount']) || empty($data['reward_name']) || empty($data['prime_rit']) || empty($data['mediation_rit']) || empty($data['sign'])){
            return [
                'is_verify'   => false,
                'reason'      => 40001,
                'error_code'  => 40001,
                'error_msg'   => '参数错误',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        /* $router_uri = request()->route()->uri();
        if($router_uri != 'api/ad_reward/gromore_x4gjvqcdgu'){
            return [
                'is_verify'   => false,
                'reason'      => 40001,
                'error_code'  => 40001,
                'error_msg'   => '访问异常更新接口',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        } */

        // 添加操作频率限制
        $user_id = $data['user_id'];
        $trans_id = $data['trans_id'];

        // 读取list.json文件并验证广告代码
        try {
            $listJsonPath = storage_path('app/adv_code/list.json');
            if (!file_exists($listJsonPath)) {
                systemLog('USER_ID:'.$user_id.' trans_id:'.$trans_id.' list.json文件不存在', 'gromore');
                // 禁用用户
                UserModel::where('id', $user_id)->update(['isset' => 2]);
                return [
                    'is_verify'   => false,
                    'reason'      => 40001,
                    'error_code'  => 40001,
                    'error_msg'   => '广告配置文件不存在',
                    'trans_id'    => $trans_id
                ];
            }

            $listJsonContent = file_get_contents($listJsonPath);
            $listData = json_decode($listJsonContent, true);
            
            if (!$listData || !isset($listData['Data']['CodeList'])) {
                systemLog('USER_ID:'.$user_id.' trans_id:'.$trans_id.' list.json文件格式错误', 'gromore');
                // 禁用用户
                return [
                    'is_verify'   => false,
                    'reason'      => 40001,
                    'error_code'  => 40001,
                    'error_msg'   => '广告配置文件格式错误',
                    'trans_id'    => $trans_id
                ];
            }

            $codeList = $listData['Data']['CodeList'];
            $mediationRit = $data['mediation_rit'];
            $foundCode = null;

            // 遍历CodeList查找匹配的CodeId
            foreach ($codeList as $codeItem) {
                if (isset($codeItem['CodeId']) && $codeItem['CodeId'] == $mediationRit) {
                    $foundCode = $codeItem;
                    break;
                }
            }

            // 检查是否找到对应的代码项
            if (!$foundCode) {
                systemLog('USER_ID:'.$user_id.' trans_id:'.$trans_id.' mediation_rit:'.$mediationRit.' 在CodeList中未找到对应项', 'gromore');
                // 禁用用户
                UserModel::where('id', $user_id)->update(['isset' => 2]);
                return [
                    'is_verify'   => false,
                    'reason'      => 40001,
                    'error_code'  => 40001,
                    'error_msg'   => '广告代码不存在',
                    'trans_id'    => $trans_id
                ];
            }

            // 检查Status是否为1
            if (!isset($foundCode['Status']) || $foundCode['Status'] != 1) {
                systemLog('USER_ID:'.$user_id.' trans_id:'.$trans_id.' mediation_rit:'.$mediationRit.' Status:'.(isset($foundCode['Status']) ? $foundCode['Status'] : 'null').' 状态不正确', 'gromore');
                // 禁用用户
                UserModel::where('id', $user_id)->update(['isset' => 2]);
                return [
                    'is_verify'   => false,
                    'reason'      => 40001,
                    'error_code'  => 40001,
                    'error_msg'   => '广告代码状态异常',
                    'trans_id'    => $trans_id
                ];
            }

            // 检查SettlementPrice
            if (isset($foundCode['SettlementPrice'])) {
                $settlementPriceSoure = $foundCode['SettlementPrice'];
                $settlementPriceSoure = (string)$settlementPriceSoure;
                $settlementPrice = (int)$foundCode['SettlementPrice'];
                if ($settlementPrice > 0) {
                    $expectedEcpm = $settlementPrice * 100;
                    $actualEcpm = (int)$data['ecpm'];
                    $settlementPriceSoure = (int)bcmul($settlementPriceSoure,'100',0);
                    if ($expectedEcpm != $actualEcpm && $settlementPriceSoure != $actualEcpm) {
                        systemLog('USER_ID:'.$user_id.' trans_id:'.$trans_id.' mediation_rit:'.$mediationRit.' SettlementPrice:'.$settlementPrice.' expectedEcpm:'.$expectedEcpm.' actualEcpm:'.$actualEcpm.' ECPM价格不匹配', 'gromore');
                        // 禁用用户
                        UserModel::where('id', $user_id)->update(['isset' => 2]);
                        return [
                            'is_verify'   => false,
                            'reason'      => 40001,
                            'error_code'  => 40001,
                            'error_msg'   => '广告价格异常',
                            'trans_id'    => $trans_id
                        ];
                    }
                }
            }

        } catch (Exception $e) {
            systemLog('USER_ID:'.$user_id.' trans_id:'.$trans_id.' 验证广告代码时发生异常:'.$e->getMessage(), 'gromore');
            // 禁用用户
            UserModel::where('id', $user_id)->update(['isset' => 2]);
            return [
                'is_verify'   => false,
                'reason'      => 40001,
                'error_code'  => 40001,
                'error_msg'   => '广告验证异常',
                'trans_id'    => $trans_id
            ];
        }


        $key = "gromore_reward_callback_limit:{$user_id}";
        // 尝试设置缓存（仅当键不存在时成功），有效期30秒
        if (!Cache::add($key, now(), 10)) {
            systemLog('USER_ID:'.$user_id.'trans_id:'.$trans_id.' gromore_reward_callback_frequent:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'gromore');
            // 如果设置失败，说明操作过于频繁
            return [
                'is_verify'   => false,
                'reason'      => 40001, // 也可以使用自定义错误码表示频繁操作
                'error_code'  => 40001,
                'error_msg'   => '操作过于频繁，', // 返回频繁操作的错误信息
                'trans_id'    => $trans_id
            ];
        }



        $sign_key = config('ad.gromore.sign_key') ?? '';
        $site_id  = config('ad.gromore.site_id') ?? '';
        $prime_rit  = config('ad.gromore.prime_rit') ?? '';
        if(empty($sign_key)){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_msg'   => '签名密钥为空',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        // 验证签名
        $sign_str = $sign_key . ':' . $data['trans_id'];
        $server_sign = hash('sha256', $sign_str);

        if($server_sign !== $data['sign']) {
            return [
                'is_verify' => false,
                'reason' => 40001,
                'error_code' => 40001,
                'error_msg' => '签名验证失败',
                'trans_id' => $data['trans_id'] ?? ''
            ];
        }

        // 验证site_id
        if($prime_rit != $data['prime_rit']) {
            return [
                'is_verify' => false,
                'reason' => 40001,
                'error_code' => 40001,
                'error_msg' => '广告位验证失败',
                'trans_id' => $data['trans_id'] ?? ''
            ];
        }

        $user_id = $data['user_id'] ?? 0;
        $user = UserModel::find($user_id);
        if(!$user){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code'  => 40001,
                'error_msg'   => '用户不存在',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }
        $user = $user->toArray();
        if($user['isset'] != 1){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '用户已禁用',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $ad_reward_gromore = AdRewardGromoreModel::where('trans_id',$data['trans_id'])->first();
        if($ad_reward_gromore){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '已存在trans_id记录',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }
        $data['extra_decode'] = '';
        try{
            if(!empty($data['extra'])){
                $extra = base64_decode($data['extra']);
                $extra = json_decode($extra,true);
                $data['extra_decode'] = json_encode($extra,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
            }
        }catch(Exception $e){
            $data['extra_decode'] = '';
        }

        $time         = time();
        $time_format  = date('Y-m-d H:i:s',$time);

        // 将ecpm转换为整数
        $data['reward_amount'] = (int)$data['ecpm'];

        AdRewardGromoreModel::create([
            'user_id' => $user_id,
            'trans_id' => $data['trans_id'],
            'reward_amount' => $data['reward_amount'],
            'reward_name' => $data['reward_name'],
            'prime_rit' => $data['prime_rit'],
            'mediation_rit' => $data['mediation_rit'],
            'sign' => $data['sign'],
            'extra' => $data['extra'] ?? '',
            'extra_decode' => $data['extra_decode'],
            'receive_over' => 0,
            'status' => 0,
            'create_time' => $time,
            'create_time_f' => $time_format,
            'update_time' => $time,
            'update_time_f' => $time_format
        ]);
        return [
            'is_verify' => true,
            'reason'    => 0,
            'error_code' => 0,
            'error_msg'   => '',
            'trans_id'    => $data['trans_id'] ?? ''
        ];
    }

    //文档地址 https://developers.adnet.qq.com/doc/android/union/union_reward_video
    public function rewardGdtCallback(){
        $data = request()->all();
        //systemLog('gdt_reward_callback:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'gdt');
        if(empty($data['userid']) || empty($data['transid']) || empty($data['pid']) || empty($data['appid']) || empty($data['sig'])){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '参数错误',
                'trans_id'    => $data['transid'] ?? ''
            ];
        }

        /* $router_uri = request()->route()->uri();
        if($router_uri != 'api/ad_reward/gdt_cvwfn8tvwu'){
            return [
                'is_verify'   => false,
                'reason'      => 40001,
                'error_code'  => 40001,
                'error_msg'   => '访问异常更新接口',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        } */

        // 添加操作频率限制
        $user_id = $data['userid'];
        $trans_id = $data['transid'];
        $key = "gdt_reward_callback_limit:{$user_id}";
        // 尝试设置缓存（仅当键不存在时成功），有效期30秒
        if (!Cache::add($key, now(), 10)) {
            systemLog('USER_ID:'.$user_id.'trans_id:'.$trans_id.' gdt_reward_callback_frequent:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'gdt');
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '操作过于频繁，', // 返回频繁操作的错误信息
                'trans_id'    => $trans_id
            ];
        }
        

        $sign_key = config('ad.gdt.sign_key') ?? '';
        $app_id   = config('ad.gdt.app_id') ?? '';
        $pid      = config('ad.gdt.pid') ?? '';
        if(empty($sign_key)){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '签名密钥为空',
                'trans_id'    => $data['transid'] ?? ''
            ];
        }

        $data['trans_id'] = $data['transid'];
        $data['sign'] = $data['sig'];

        // 验证签名
        $sign_str =  $data['trans_id'] . ':' . $sign_key;
        $server_sign = hash('sha256', $sign_str);

        if($server_sign !== $data['sign']){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '签名验证失败',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        // 验证app_id
        if($app_id != $data['appid']){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => 'app_id验证失败',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        // 验证pid
        if($pid != $data['pid']){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => 'pid验证失败',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }


        $data['user_id'] = $data['userid'];
        $data['app_id'] = $data['appid'];
        $data['extra'] = $data['extrainfo'] ?? '';

        unset($data['userid']);
        unset($data['transid']);
        unset($data['appid']);
        unset($data['sig']);

        $user = UserModel::find($data['user_id']);
        if(!$user){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '用户不存在',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $user = $user->toArray();
        if($user['isset'] != 1){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '用户已禁用',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $ad_reward_gdt = AdRewardGdtModel::where('trans_id',$data['trans_id'])->first();
        if($ad_reward_gdt){
            return [
                'is_verify' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '已存在trans_id记录',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $data['extra_decode'] = '';
        try{
            if(!empty($data['extra'])){
                $extra = base64_decode($data['extra']);
                $extra = json_decode($extra,true);
                $data['extra_decode'] = json_encode($extra,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
            }
        }catch(Exception $e){
            $data['extra_decode'] = '';
        }

        $time         = time();
        $time_format  = date('Y-m-d H:i:s',$time);

        //优量汇回调没有返回ecpm

        AdRewardGdtModel::create([
            'user_id' => $data['user_id'],
            'trans_id' => $data['trans_id'],
            'reward_amount' => 0,
            'reward_name' => 'gdt',
            'pid' => $data['pid'],
            'app_id' => $data['app_id'],
            'sign' => $data['sign'],
            'extra' => $data['extra'] ?? '',
            'extra_decode' => $data['extra_decode'],
            'receive_over' => 0,
            'status' => 0,
            'create_time' => $time,
            'create_time_f' => $time_format,
            'update_time' => $time,
            'update_time_f' => $time_format
        ]);
        return [
            'is_verify' => true,
            'reason'    => 0,
            'error_code' => 0,
            'error_msg'   => '',
            'trans_id'    => $data['trans_id'] ?? ''
        ];
    }

    public function rewardKsCallback(){
        $data = request()->all();
        //systemLog('ks_reward_callback:'.json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), 'ks');

        if( empty($data['userId']) ||
            empty($data['transId']) ||
            empty($data['sign']) ||
            empty($data['amount'])
        ){
            return [
                'isValid' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '参数错误',
                'trans_id'    => $data['transId'] ?? ''
            ];
        }

        $sign_key = config('ad.ks.sign_key') ?? '';
        if(empty($sign_key)){
            return [
                'isValid' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '签名密钥为空',
                'trans_id'    => $data['transId'] ?? ''
            ];
        }

        $data['trans_id'] = $data['transId'];

        //验证签名
        $sign_str =  $sign_key . ':' . $data['trans_id'];
        $server_sign = strtolower(md5($sign_str));

        if($server_sign !== strtolower($data['sign'])){
            return [
                'isValid' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '签名验证失败',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $data['user_id'] = $data['userId'];
        $data['amount'] = $data['amount'];

        $user = UserModel::find($data['user_id']);
        if(!$user){
            return [
                'isValid' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '用户不存在',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $user = $user->toArray();
        if($user['isset'] != 1){
            return [
                'isValid' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '用户已禁用',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $ad_reward_ks = AdRewardKsModel::where('trans_id',$data['trans_id'])->first();
        if($ad_reward_ks){
            return [
                'isValid' => false,
                'reason'    => 40001,
                'error_code' => 40001,
                'error_msg'   => '已存在trans_id记录',
                'trans_id'    => $data['trans_id'] ?? ''
            ];
        }

        $time         = time();
        $time_format  = date('Y-m-d H:i:s',$time);

        AdRewardKsModel::create([
            'user_id' => $data['user_id'],
            'trans_id' => $data['trans_id'],
            'reward_amount' => $data['amount'],
            'reward_name' => 'ks',
            'pid' => '',
            'app_id' => '',
            'sign' => $data['sign'],
            'extra' => '',
            'extra_decode' => '',
            'receive_over' => 0,
            'status' => 0,
            'create_time' => $time,
            'create_time_f' => $time_format,
            'update_time' => $time,
            'update_time_f' => $time_format
        ]);
        return [
            'isValid' => true,
            'reason'    => 0,
            'error_code' => 0,
            'error_msg'   => '',
            'trans_id'    => $data['trans_id'] ?? ''
        ];
    }

    /**
     * 处理用户登录记录
     * @param int $userId 用户ID
     * @param string|array $login_info 用户登录信息
     * @return void
     */
    public function handleLoginRecord($userId, $login_info=[])
    {
        // 确保 $login_info 是数组类型
        if(!is_array($login_info)) {
            return false;
        }

        $now = time();

        // 查询最近一条登录记录
        $lastLogin = UserLoginModel::where('user_id', $userId)
            ->orderBy('login_time', 'desc')
            ->first();

        // 如果存在最近登录记录且时间间隔小于10秒,则不创建新记录
        /* if($lastLogin && ($now - $lastLogin->login_time < 3600) && date('Y-m-d', $now) == date('Y-m-d', $lastLogin->login_time)) {
            return;
        } */

        if($lastLogin && ($now - $lastLogin->login_time < 10)) {
            return;
        }

        if(empty($login_info['ip'])){
            $login_info['ip'] = Tools::getIp();
        }

        // 创建新的登录记录
        UserLoginModel::create([
            'user_id' => $userId,
            'login_time' => $now,
            'ip' => $login_info['ip'] ?? '',
            'ip_address' => $login_info['ip_address'] ?? '',
            'm_type' => $login_info['m_type'] ?? 1,
            'm_version' => $login_info['m_version'] ?? '',
            'm_brand' => $login_info['m_brand'] ?? '',
            'm_model' => $login_info['m_model'] ?? '',
            'm_device_id' => $login_info['m_device_id'] ?? '',
            'm_network' => $login_info['m_network'] ?? 0,
            'm_netserver' => $login_info['m_netserver'] ?? 0,
            'm_simulator' => $login_info['m_simulator'] ?? 0,
            'adv_num' => 0,
            'money' => 0,
            'withdraw' => 0,
            'invite_num' => 0,
            'create_time' => $now,
            'update_time' => $now
        ]);
    }

    public function loginSms(){
        $data = request()->all();
        $phone = $data['phone'] ?? '';
        $pwd = $data['pwd'] ?? '';
        if(empty($phone) || empty($pwd)){
            throw new MyException('参数错误');
        }
        if(!Tools::isMobile($phone)){
            throw new MyException('手机号格式错误');
        }

        $user = UserModel::where('phone',$phone)->first();
        if(!$user){
            throw new MyException('您的账号不存在');
        }

        if($user->pwd != md5($pwd)){
            throw new MyException('您的账号密码错误');
        }
        //已删除的账号
        if($user->delete_time>0){
            throw new MyException('您的账号不存在');
        }

        if($user->isset==2){
            throw new MyException('您的账号已禁用');
        }

        $user->login_time = time();
        $user->ip = Tools::getIp();

        if(empty($user->invite_code)){
            $user->invite_code = $this->generateInviteCode();
        }

        $user->save();


        $admin_id = $user->admin_id;

        if($user->identity !=2 || $user->admin_id<=0){
            //如果不是代理 则需要设置成代理身份
            if($user->admin_id>0){
                $user->identity = 2;
                $user->save();
            }
            //如果代理ID为0 则需要设置成代理身份
            if( $user->admin_id <= 0 ){
                DB::beginTransaction();
                try {
                    //检查手机号是否已存在于admin_user表
                    $existingAdmin = AdminUserModel::where('phone', $user->phone)->first();

                    if($existingAdmin) {
                        //如果存在,只更新user_id
                        $existingAdmin->user_id = $user->id;
                        $existingAdmin->save();
                        $admin_id = $existingAdmin->id;
                    } else {
                        //不存在则创建新记录
                        $adminData = [
                            'user_id' => $user->id,
                            'username' => $user->phone,
                            'phone' => $user->phone,
                            'password' => Tools::agentPwd($pwd),
                            'real_name' => 'app_' . $user->id,
                            'status' => 1,
                            'type' => 2,
                            'create_time' => time(),
                            'update_time' => time()
                        ];
                        $admin = AdminUserModel::create($adminData);
                        $admin_id = $admin->id;
                    }
                    //更新user表的admin_id和identity
                    $user->admin_id = $admin_id;
                    $user->identity = 2;
                    $user->save();
                    DB::commit();
                } catch(\Exception $e) {
                    DB::rollBack();
                    throw new MyException('用户登录失败,请联系客服');
                }
            }
        }

        // 处理登录记录

        $login_info = [
            'ip' => Tools::getIp(),
            'ip_address' => '',
            'm_type' => $data['m_type'] ?? 1,
            'm_version' => $data['m_version'] ?? '',
            'm_brand' => $data['m_brand'] ?? '',
            'm_model' => $data['m_model'] ?? '',
            'm_device_id' => $data['m_device_id'] ?? '',
            'm_network' => $data['m_network'] ?? 0,
            'm_netserver' => $data['m_netserver'] ?? 0,
            'm_simulator' => $data['m_simulator'] ?? 0,
        ];
        $this->handleLoginRecord($user->id, $login_info);
        $this->handleRegOrLoginRecord($user->id, $admin_id, 2, $login_info);
        if(!empty($login_info['m_device_id']) && 1==2){
            if($user->pid>0 && $user->is_cheat == 0){
                $user_relation = UserRelationModel::where('user_id',$user->id)->first();
                if($user_relation && $user_relation->is_cheat == 0){
                    $user_login = UserLoginModel::where('m_device_id',$login_info['m_device_id'])
                                                ->where('user_id',$user->pid)
                                                ->orderBy('id','desc')
                                                ->first();
                    if($user_login){
                        //设备号一样  再查询最近一次登录的IP  如果IP一样 则表示作弊
                        $user_pid_ip = UserLoginModel::where('user_id',$user->pid)
                                                ->orderBy('id','desc')
                                                ->first();
                        if($user_pid_ip && $user_pid_ip->ip == $login_info['ip']){
                            $user_relation->is_cheat = 1;
                            $user_relation->save();
                            $user->is_cheat = 1;//新增字段标志
                            $user->save();
                        }
                    }
                }
            }
        }


        $user = $user->toArray();
        $pwd = Tools::jsEncrypt($user['phone'].':'.$user['pwd'],$user['pwd']);
        $user['token']=$user['phone'].':'.$pwd;
        return $user;
    }

    public function bindCard($user_id){
        $data = request()->all();
        $image = $data['weixin_image'] ?? '';
        if(empty($image)){
            throw new MyException('请上传微信收款码');
        }
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }

        if(Str::startsWith($image,'/')){
            $image = uploadFilePathNoPre($image);
        }
        //去除可能出现的重复情况
        $image = str_ireplace(request()->getSchemeAndHttpHost().request()->getSchemeAndHttpHost(),request()->getSchemeAndHttpHost(),$image);
        $image = str_ireplace(request()->getSchemeAndHttpHost().'//upload',request()->getSchemeAndHttpHost().'/upload',$image);

        $user->weixin_image = $image;
        $user->save();
        return [];
    }

    public function withdrawInfo($user){
        $user = UserModel::find($user['id']);
        if(!$user){
            throw new MyException('用户不存在');
        }
        $desc = getAppConfig('withdrawal','desc');

        // 将提现说明文本按\r\n分割并过滤空值
        if(!empty($desc)) {
            $desc = array_values(array_filter(explode("\r\n", $desc), function($item) {
                return !empty(trim($item));
            }));
        } else {
            $desc = [];
        }

        $site_name = getAppConfig('site','site_name');

        $desc[] = (count($desc)+1)."、您当前使用的应用名称【".$site_name."】登录账号：【".$user['phone']."】，有问题可以通过 红包提现->在线客服 反馈";

        $title = getAppConfig('withdrawal','title');

        return [
            'weixin_image' => $user->weixin_image ? $user->weixin_image : '',
            'title' => $title,
            'desc' => $desc
        ];
    }

    public function canAd($user){
        $user_id = $user['id'];
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        $day_num = $user['ad_number'];
        $interval_time = (int)getAppConfig('ad','interval_time');
        if(empty($day_num)){
            $day_num = (int)getAppConfig('ad','number');
        }

        // 检查今日广告观看次数
        $count = AdvertisementModel::where('user_id', $user_id)
            ->where('can_time', date('Y-m-d'))
            ->count();

        if($count >= $day_num){
            throw new MyException('今天看广告次数已用完');
        }

        //当前的IP看了多少条广告
        $ip = Tools::getIp();
        if(!empty($ip)){
            $count = AdvertisementModel::where('ip', $ip)
                ->where('can_time', date('Y-m-d'))
                ->count();
            if($count >= $day_num){
                throw new MyException('今天看广告次数已用完');
            }
        }


        //当前的设备看了多少条广告
        $m_device_id = UserLoginModel::where('user_id', $user_id)->orderBy('id', 'desc')->value('m_device_id');
        if(!empty($m_device_id) && 1==2){
            $count = AdvertisementModel::where('m_device_id', $m_device_id)
            ->where('can_time', date('Y-m-d'))
            ->count();
            if($count >= $day_num){
                throw new MyException('今天看广告次数已用完');
            }
        }
        // 检查广告观看时间间隔
        $lastAd = AdvertisementModel::where('user_id', $user_id)
            ->orderBy('create_time', 'desc')
            ->first();

        if($lastAd && (time() - strtotime($lastAd->create_time) < $interval_time)){
            throw new MyException('广告间隔时间没到');
        }

        return [];
    }

    public function packetDescribe($user){
        $user_id = $user['id'];
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        return $this->userAdInfo($user_id);
    }

    public function collectPacketOne($user){
        return [];
    }

    private function convert($number){
        $formatted_number = sprintf("%.2f", substr(sprintf("%.4f",$number),0,-2));
        if($formatted_number=='0.00'){
            return 0.01;
        }else{
            return $formatted_number;
        }
    }

    public function withdrawList($user_id){
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }


        // 获取分页参数
        $page = request()->input('page_no', $this->page_no);
        $perPage = request()->input('per_page', $this->per_page);

        // 构建查询
        $query = Withdraw::where('user_id', $user_id)
            ->select('id', 'money', 'real_money', 'create_time', 'status', 'reply')
            ->orderBy('create_time', 'desc');

        // 获取总数
        $count = $query->count();

        // 获取分页数据
        $list = $query->forPage($page, $perPage)->get()->toArray();

        return [
            'list' => $list,
            'page_no' => (int)$page,
            'page_size' => (int)$perPage,
            'count' => $count
        ];
    }

    public function withdrawAdd($user){
        $data = request()->all();
        $user_id = $user['id'];

        // 开启事务并加锁
        DB::beginTransaction();
        try {

            $key = "request_withdraw_limit:{$user_id}";
            // 尝试设置缓存（仅当键不存在时成功）
            if (!Cache::add($key, now(), 15)) {
                throw new MyException('提现操作过于频繁，请稍候再试');
            }


            // 使用 lockForUpdate 加排他锁
            $user = UserModel::where('id', $user_id)->lockForUpdate()->first();
            if(!$user){
                throw new MyException('用户不存在');
            }

            if($user['isset'] != 1){
                throw new MyException('您的账号已失效，请联系客服');
            }

            if(config('ad.push_to_third')==0){
                if(empty($user['weixin_image'])){
                    throw new MyException('请先绑定微信收款码');
                }
            }

            $money = $data['money'];
            if($money<=0){
                throw new MyException('提现金额不能小于0');
            }

            if($user['money']<$money){
                throw new MyException('您的余额不足');
            }

            $money_item = WithdrawMoney::query()
                    ->where('delete_time',0)
                    ->orWhere('delete_time',null)
                    ->where('money',$money)
                    ->select('id', 'money', 'day_num','commission')
                    ->first();

            if(!$money_item){
                throw new MyException('提现金额不符合要求');
            }

            $count = Withdraw::query()->where([
                        'user_id'=>$user_id,
                        'money'=>$money,
                        'add_time'=>date('Y-m-d'),
                    ])->count();

            if($money_item['day_num']>0 && $money_item['day_num']<=$count){
                throw new MyException('您今天不能再提现该金额');
            }

            $pt_money = 0;
            $real_money = $money;
            if($money_item['commission']>0){
                $commission = $money_item['commission'];
                $charge     = $commission*0.01;
                $pt_money   = $money*$charge;
                $real_money = $money-$pt_money;
            }

            // 创建提现记录
            $withdraw_id = Withdraw::query()->insertGetId([
                'user_id'=>$user_id,
                'money'=>$money,
                'real_money'=>$real_money,
                'add_time'=>date('Y-m-d'),
                'create_time'=>time(),
                'update_time'=>time()
            ]);

            // 更新用户余额
            $user->money = $user->money-$money;
            $user->save();


            if(config('ad.push_to_third')==1){
                // 调用封装的方法推送数据到第三方
                $this->pushWithdrawDataToThird(10, $user, $real_money, $withdraw_id);
            }

            // 提交事务
            DB::commit();
            return [];

        } catch(\Exception $e) {
            // 回滚事务
            DB::rollBack();
            throw new MyException($e->getMessage());
        }
    }

    /**
     * 推送提现数据到第三方平台
     * @param int $type 类型：10 第三方平台提现 11 第三方平台任务奖励
     * @param object $user 用户对象
     * @param float $real_money 实际提现金额
     * @param int $withdraw_id 提现ID
     * @return bool 是否成功
     * @throws MyException 如果操作失败则抛出异常
     */
    public function pushWithdrawDataToThird($type, $user, $real_money, $withdraw_id)
    {
        // 开启第三方数据推送
        $appid    = config('ad.appid');
        $appkey   = config('ad.appkey');
        $base_url = config('ad.base_url');

        $req_url = $base_url.'/api/receiveThirdPartyWithdrawalData';

        $req_data = [];
        $req_data['ad_platform_user_id'] = $user->id;
        $req_data['mobile']              = $user->phone;
        $req_data['price']               = $real_money;
        $req_data['order_id']            = $withdraw_id;
        $req_data['type']                = $type; // 10 第三方平台提现 11 第三方平台任务奖励
        ksort($req_data);

        $encrypted = AdSecurity::encrypt($req_data, $appkey);
        $encrypted['appid'] = $appid;

        try {
            $client = new Client([
                'verify' => false, // 忽略SSL证书验证
                'timeout' => 10,   // 设置超时时间
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ],
                // 关键修改：设置http_errors为false，这样Guzzle不会为4xx和5xx响应抛出异常
                'http_errors' => false
            ]);

            $response = $client->post($req_url, [
                'json' => $encrypted
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            $result = json_decode($responseBody, true);

            // 检查HTTP状态码
            if ($statusCode >= 400) {
                // 处理HTTP错误响应
                $errorMessage = '操作失败';
                if ($result && isset($result['message'])) {
                    $errorMessage = $result['message'];
                } elseif ($result && isset($result['msg'])) {
                    $errorMessage = $result['msg'];
                }
                throw new MyException($errorMessage);
            }

            // 检查业务逻辑返回码
            if (isset($result['code']) && $result['code'] == 200
               && isset($result['status']) && $result['status'] == 200
            )
            {
                if(!empty($result['data'])){
                    $result_data = $result['data'];
                    if(!empty($result_data['third_user_id']) && !empty($result_data['third_order_id'])){
                        if($type == 10){
                            WithdrawThird::query()->insert([
                                'user_id'           => $user->id,
                                'order_id'          => $withdraw_id,
                                'third_user_id'     => $result_data['third_user_id'],
                                'third_order_id'    => $result_data['third_order_id'],
                                'add_time'          => date('Y-m-d'),
                                'create_time'       => time(),
                                'update_time'       => time()
                            ]);
                        }
                        if($type == 11){
                            WithdrawRewardThird::query()->insert([
                                'user_id'           => $user->id,
                                'order_id'          => $withdraw_id,
                                'third_user_id'     => $result_data['third_user_id'],
                                'third_order_id'    => $result_data['third_order_id'],
                                'add_time'          => date('Y-m-d'),
                                'create_time'       => time(),
                                'update_time'       => time()
                            ]);
                        }
                        return true;
                    }else{
                        throw new MyException('操作失败');
                    }
                }else{
                    throw new MyException('操作失败');
                }
            }else{
                // 如果API返回错误，则返回错误信息
                $errorMessage = '操作失败';
                if (isset($result['message'])) {
                    $errorMessage = $result['message'];
                } elseif (isset($result['msg'])) {
                    $errorMessage = $result['msg'];
                }
                throw new MyException($errorMessage);
            }
        } catch (\Exception $e) {
            // 如果是我们自己抛出的MyException，直接重新抛出
            if ($e instanceof MyException) {
                throw $e;
            }
            
            // 处理其他异常（如网络异常、JSON解析异常等）
            throw new MyException('网络请求失败：' . $e->getMessage());
        }
    }

    public function moneyList($user_id){
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        // 获取分页参数
        $page = request()->input('page_no', $this->page_no);
        $perPage = request()->input('per_page', $this->per_page);

        $query = RedPacketModel::select('red_packet.*', 'user.phone as from_user_phone')
            ->leftJoin('user', 'red_packet.from_user_id', '=', 'user.id')
            ->where('red_packet.user_id', $user_id)
            ->select('red_packet.*', 'user.phone as from_user_phone')
            ->orderBy('red_packet.create_time', 'desc');
        $count = $query->count();
        $list = $query->forPage($page, $perPage)->get()->toArray();

        $site_name = getAppConfig('site','site_name');
        $site_name = Str::substr($site_name, 0, 4);
        $phone = $user['phone'];
        if($phone){
            $phone = substr($phone, 0, 2) . '******' . substr($phone, -3);
        }
        // 处理手机号隐私
        foreach($list as &$item) {
            if(!empty($item['from_user_phone']) && preg_match('/^1\d{10}$/', $item['from_user_phone'])) {
                $item['from_user_phone'] = substr($item['from_user_phone'], 0, 3) . '****' . substr($item['from_user_phone'], -4);
            } else {
                $item['from_user_phone'] = '';
            }
            $item['create_time'] = $item['create_time']."[".$site_name."-".$phone."]";
        }

        return [
            'list' => $list,
            'page_no' => (int)$page,
            'page_size' => (int)$perPage,
            'count' => $count
        ];
    }



    /**
     * 生成唯一的6位注册码
     * @return string 生成的注册码
     * @throws MyException 当无法生成唯一注册码时抛出异常
     */

    public function generateInviteCode()
    {
        $maxAttempts = 30; // 最大尝试次数
        $attempts = 0;
        do {
            // 生成6位随机字符串(只包含数字和大写字母,排除易混淆的字符)
            $code = Tools::generateRandomStr(6);
            // 检查是否已存在
            $exists = UserModel::where('invite_code', $code)->exists();
            $attempts++;
            // 如果不存在则返回该码
            if(!$exists) {
                return $code;
            }
        } while($attempts < $maxAttempts);

        // 如果尝试次数过多仍未成功,则抛出异常
        throw new MyException('无法生成唯一邀请码,请重试');
    }

    public function regH5()
    {
        if(config('ad.register_from_third') == 0){
            throw new MyException('请通过小程序授权注册账号');
        }
        $data = request()->all();

        // 验证必填参数
        if(empty($data['phone']) || empty($data['password']) || empty($data['confirmPassword'])
            || empty($data['captcha']) || empty($data['imageKey']) || empty($data['reg_code'])) {
            throw new MyException('请填写完整注册信息');
        }

        // 验证密码是否一致
        if($data['password'] !== $data['confirmPassword']) {
            throw new MyException('两次密码输入不一致');
        }

        // 验证图形验证码
        if(!captcha_api_check($data['captcha'], $data['imageKey'])) {
            return ['result'=>40001,'error'=>'验证码错误'];
        }

        // 重新组织参数调用register
        $registerData = [
            'phone' => $data['phone'],
            'pwd' => $data['password'], // 重命名password为pwd
            'reg_code' => $data['reg_code']
        ];

        // 将参数放入request中
        request()->merge($registerData);

        // 调用原register方法
        return $this->register();
    }

    public function regFromThird()
    {
        $data = request()->all();
        // 验证加密数据
        if (empty($data['encrypted_data']) || empty($data['sign'])) {
            throw new MyException('参数错误');
        }
        // 使用 AdSecurity 工具类解密数据
        $appKey = config('ad.appkey');
        $decryptedData = AdSecurity::decryptFromClient([
            'encrypted_data' => $data['encrypted_data'],
            'sign' => $data['sign']
        ], $appKey);


        if ($decryptedData === false) {
            throw new MyException('数据错误');
        }
        $data = $decryptedData;
        if(empty($data['timestamp'])){
            throw new MyException('数据错误');
        }
        if(time()-$data['timestamp']>60){
            throw new MyException('数据错误');
        }
        if(empty($data['mobile']) || empty($data['password'])){
            throw new MyException('数据错误');
        }

        // 验证手机号格式
        if(!Tools::isMobile($data['mobile'])){
            throw new MyException('手机号格式错误');
        }

        // 验证密码长度
        if(strlen($data['password']) < 6 || strlen($data['password']) > 16){
            throw new MyException('密码长度必须大于等于6位或者小于等于16位');
        }
        $data['phone'] = $data['mobile'];
        $is_exists = UserModel::where('phone',$data['phone'])->select('id')->first();
        if($is_exists){
            throw new MyException('该账号已注册');
        }

        $pid = 0;
        $pid_2 = 0;
        $pid_str = config('ad.system_tg_code');
        if(stristr($pid_str,'_')){
            $pid = explode('_',$pid_str)[0];
        }else{
            $pid = $pid_str;
        }
        if(!empty($data['pid_mobile'])){
            $inviter = UserModel::where('phone', $data['pid_mobile'])->select('id','isset','pid')->first();
            if($inviter && $inviter->isset == 1){
                $pid = $inviter->id;
                $pid_2 = $inviter->pid;
            }
        }

        $pid = (int)$pid;
        $pid_2 = (int)$pid_2;
        $invite_code = $this->generateInviteCode();
        $ad_number = (int)getAppConfig('ad','number');
        if($ad_number<0){
            $ad_number = 0;
        }
        if($ad_number>200){
            $ad_number = 200;
        }

        DB::beginTransaction();
        try {
            // 创建用户
            $userData = [
                'pid' => $pid,
                'pid_2' => $pid_2,
                'phone' => $data['phone'],
                'pwd' => md5($data['password']),
                'invite_code' => $invite_code,
                'identity' => 2,
                'commission' => 0,
                'user_commission' => 0,
                'admin_commission' => 0,
                'money' => 0,
                'login_time' => time(),
                'add_time' => time(),
                'ad_number' => $ad_number,
                'ip' => Tools::getIp(),
                'login_type' => 0,
                'weixin_image' => '',
                'alipay_image' => '',
                'create_time' => time(),
                'update_time' => time()
            ];
            $user = UserModel::create($userData);

            $adminData = [
                'user_id' => $user->id,
                'username' => $data['phone'],//用手机号登录
                'phone' => $data['phone'],
                'password' => Tools::agentPwd($data['password']),
                'real_name' => 'mini_'.$user->id,
                'status' => 1,
                'type' => 2,
                'create_time' => time(),
                'update_time' => time()
            ];
            $admin = AdminUserModel::create($adminData);
            $user->admin_id = $admin->id;
            $user->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // 判断是否是唯一索引冲突异常
            if(str_contains($e->getMessage(), 'Duplicate entry') && str_contains($e->getMessage(), 'phone')) {
                throw new MyException('该手机号已被注册,请勿重复提交');
            }
            throw new MyException('注册失败');
        }


        //注册成功后 记录登录信息
        $login_info = [
            'ip' => Tools::getIp(),
            'ip_address' => '',
            'm_type' => $data['m_type'] ?? 1,
            'm_version' => $data['m_version'] ?? '',
            'm_brand' => $data['m_brand'] ?? '',
            'm_model' => $data['m_model'] ?? '',
            'm_device_id' => $data['m_device_id'] ?? '',
            'm_network' => $data['m_network'] ?? 0,
            'm_netserver' => $data['m_netserver'] ?? 0,
            'm_simulator' => $data['m_simulator'] ?? 0,
        ];
        $this->handleLoginRecord($user->id, $login_info);
        $this->handleRegOrLoginRecord($user->id, $admin->id, 1, $login_info);

        $user_info = [
            'user_id' => $user->id,
            'mobile'  => $user->phone,
            'pid'     => $pid
        ];

        return $user_info;

    }

    public function updateFromThird()
    {
        $data = request()->all();
        // 验证加密数据
        if (empty($data['encrypted_data']) || empty($data['sign'])) {
            throw new MyException('参数错误');
        }
        // 使用 AdSecurity 工具类解密数据
        $appKey = config('ad.appkey');
        $decryptedData = AdSecurity::decryptFromClient([
            'encrypted_data' => $data['encrypted_data'],
            'sign' => $data['sign']
        ], $appKey);

        if ($decryptedData === false) {
            throw new MyException('数据错误');
        }
        $data = $decryptedData;
        if(empty($data['timestamp'])){
            throw new MyException('数据错误');
        }
        if(time()-$data['timestamp']>60){
            throw new MyException('数据错误');
        }
        if(empty($data['phone']) || empty($data['password']) || empty($data['user_id'])){
            throw new MyException('数据错误');
        }

        // 验证手机号格式
        if(!Tools::isMobile($data['phone'])){
            throw new MyException('手机号格式错误');
        }

        // 验证密码长度
        if(strlen($data['password']) < 6 || strlen($data['password']) > 16){
            throw new MyException('密码长度必须大于等于6位或者小于等于16位');
        }

        $user = UserModel::query()->where('id',$data['user_id'])
                                ->where('phone',$data['phone'])
                                ->first();
        if(!$user){
            throw new MyException('用户不存在');
        }

        if($user->isset !=1){
            throw new MyException('用户已禁用');
        }

        $user->pwd = md5($data['password']);
        $user->save();
        return [];
    }

    /**
     * 第三方绑定账号接口
     * 根据传递的加密数据解密参数，解密后参数必须包含手机号和密码
     * 根据账号密码查询信息是否正确，返回相关的值
     *
     * @return array 包含绑定结果的数组
     * @throws MyException 当参数错误或验证失败时抛出异常
     */
    public function bindFromThird()
    {
        $data = request()->all();
        // 验证加密数据
        if (empty($data['encrypted_data']) || empty($data['sign'])) {
            throw new MyException('参数错误');
        }

        // 使用 AdSecurity 工具类解密数据
        $appKey = config('ad.appkey');
        $decryptedData = AdSecurity::decryptFromClient([
            'encrypted_data' => $data['encrypted_data'],
            'sign' => $data['sign']
        ], $appKey);

        if ($decryptedData === false) {
            throw new MyException('数据解密失败');
        }

        $data = $decryptedData;

        // 验证时间戳，防止重放攻击
        if(empty($data['timestamp'])){
            throw new MyException('数据错误：缺少时间戳');
        }

        if(time() - $data['timestamp'] > 60){
            throw new MyException('数据已过期');
        }

        // 验证必要参数
        if(empty($data['mobile']) || empty($data['password'])){
            throw new MyException('数据错误：缺少手机号或密码');
        }

        // 验证手机号格式
        if(!Tools::isMobile($data['mobile'])){
            throw new MyException('手机号格式错误');
        }

        // 验证密码长度
        if(strlen($data['password']) < 6 || strlen($data['password']) > 16){
            throw new MyException('密码长度必须大于等于6位或者小于等于16位');
        }

        // 查询用户信息
        $user = UserModel::where('phone', $data['mobile'])->first();

        // 构建返回结果
        $result = [
            'can_bind' => false,
            'reason' => '',
            'user_info' => null
        ];

        // 检查用户是否存在
        if (!$user) {
            $result['reason'] = '账号不存在';
            return $result;
        }

        // 检查用户状态
        if ($user->isset != 1) {
            $result['reason'] = '账号已禁用';
            return $result;
        }

        // 检查密码是否正确
        if ($user->pwd != md5($data['password'])) {
            $result['reason'] = '密码不正确';
            return $result;
        }

        // 验证通过，可以绑定
        $result['can_bind'] = true;

        // 返回基本用户信息
        $result['user_info'] = [
            'user_id' => $user->id,
            'phone' => $user->phone,
            'pid' => $user->pid
        ];

        return $result;
    }




    public function withdrawNotify()
    {
        $data = request()->all();
        // 验证加密数据
        if (empty($data['encrypted_data']) || empty($data['sign'])) {
            throw new MyException('参数错误');
        }
        $appKey = config('ad.appkey');
        $decryptedData = AdSecurity::decryptFromClient([
            'encrypted_data' => $data['encrypted_data'],
            'sign' => $data['sign']
        ], $appKey);

        if ($decryptedData === false) {
            throw new MyException('数据错误');
        }
        $data = $decryptedData;
        if(empty($data['third_order_id'])){
            throw new MyException('数据错误');
        }
        if(empty($data['third_user_id'])){
            throw new MyException('数据错误');
        }
        if(empty($data['status'])){
            throw new MyException('数据错误');
        }
        if(empty($data['message'])){
            throw new MyException('数据错误');
        }

        if(empty($data['money'])){
            throw new MyException('数据错误');
        }

        $withdraw_third = WithdrawThird::query()->where('third_order_id',$data['third_order_id'])
                                            ->where('third_user_id',$data['third_user_id'])
                                            ->first();
        if(!$withdraw_third){
            throw new MyException('订单记录不存在');
        }

        $order_id = $withdraw_third->order_id;
        $user_id  = $withdraw_third->user_id;
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        $withdraw = Withdraw::query()->where('id',$order_id)->first();
        if(!$withdraw){
            throw new MyException('提现记录不存在');
        }

        if($withdraw->status != 1){
            throw new MyException('提现记录状态错误');
        }

        if($withdraw->real_money != $data['money']){
            throw new MyException('提现金额错误');
        }

        $withdraw->status = $data['status'];
        if($data['status'] == 3){
            $withdraw->reply = $data['message'];
        }
        $withdraw->adopt_time = date("Y-m-d H:i:s");
        $withdraw->update_time = time();
        $withdraw->save();

        if($data['status'] == 2){
            $user->withdrawal_money = $user->withdrawal_money + $withdraw->real_money;
            $user->save();
        }
        if($data['status'] == 3){
            $user->money = $user->money + $withdraw->real_money;
            $user->save();
        }
        return [];
    }

    public function withdrawRewardNotify()
    {
        $data = request()->all();
        // 验证加密数据
        if (empty($data['encrypted_data']) || empty($data['sign'])) {
            throw new MyException('参数错误');
        }
        // 使用 AdSecurity 工具类解密数据
        $appKey = config('ad.appkey');
        $decryptedData = AdSecurity::decryptFromClient([
            'encrypted_data' => $data['encrypted_data'],
            'sign' => $data['sign']
        ], $appKey);

        if ($decryptedData === false) {
            throw new MyException('数据错误');
        }
        $data = $decryptedData;
        if(empty($data['third_order_id'])){
            throw new MyException('数据错误');
        }
        if(empty($data['third_user_id'])){
            throw new MyException('数据错误');
        }
        if(empty($data['status'])){
            throw new MyException('数据错误');
        }
        if(empty($data['message'])){
            throw new MyException('数据错误');
        }
        if(empty($data['money'])){
            throw new MyException('数据错误');
        }

        $withdraw_third = WithdrawRewardThird::query()->where('third_order_id',$data['third_order_id'])
                                            ->where('third_user_id',$data['third_user_id'])
                                            ->first();
        if(!$withdraw_third){
            throw new MyException('订单记录不存在');
        }

        $order_id = $withdraw_third->order_id;
        $user_id  = $withdraw_third->user_id;
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        $withdraw = WithdrawReward::query()->where('id',$order_id)->first();
        if(!$withdraw){
            throw new MyException('提现记录不存在');
        }

        if($withdraw->status != 1){
            throw new MyException('提现记录状态错误');
        }

        if($withdraw->real_money != $data['money']){
            throw new MyException('提现金额错误');
        }

        $withdraw->status = $data['status'];
        if($data['status'] == 3){
            $withdraw->reply = $data['message'];
        }
        $withdraw->adopt_time = date("Y-m-d H:i:s");
        $withdraw->update_time = time();
        $withdraw->save();

        if($data['status'] == 2){
            UserAdReward::query()->where('user_id',$user_id)
                                ->update([
                                    'ad_reward_num' => DB::raw('ad_reward_num + '.$withdraw->real_money),
                                    'update_time' => time()
                                ]);
        }
        return [];
    }

    public function register()
    {
        if(config('ad.register_from_third') == 0){
            throw new MyException('请通过小程序授权注册账号');
        }
        $data = request()->all();
        $phone = $data['phone'] ?? '';
        $pwd = $data['pwd'] ?? '';
        $reg_code = $data['reg_code'] ?? '';


        // 验证必填参数
        if(empty($phone) || empty($pwd) || empty($reg_code)){
            throw new MyException('手机号、密码和注册码不能为空');
        }

        // 验证手机号格式
        if(!Tools::isMobileReg($phone)){
            throw new MyException('手机号格式错误');
        }

        // 验证密码长度
        if(strlen($pwd) < 6){
            throw new MyException('密码长度不能小于6位');
        }

        // 处理注册码
        $reg_code = strtolower($reg_code);
        if(strlen($reg_code) != 6){
            throw new MyException('注册码格式不正确');
        }


        // 验证注册码是否存在
        $inviter = UserModel::where('invite_code', $reg_code)->first();
        if(!$inviter){
            throw new MyException('无效的注册码');
        }


        if($inviter->isset != 1){
            throw new MyException('该注册码已失效');
        }

        $is_exists = UserModel::where('phone',$phone)->first();
        if($is_exists){
            throw new MyException('该手机号已注册');
        }

        //增加一步验证 代理是否存在  新功能 用户注册自动成为代理
        $admin = AdminUserModel::where('phone',$phone)->first();
        if($admin){
            throw new MyException('该手机号已注册');
            /* if($admin->status != 1){
                throw new MyException('该注册码已失效');
            } */
        }

        //验证设备是否已注册 取消该验证 允许注册
        if(!empty($data['m_device_id']) && 1==2){
            $user_relation = UserRelationModel::where('m_device_id',$data['m_device_id'])->select('update_time','bind_type')->first();
            if($user_relation){
                $update_time = $user_relation->update_time;
                if(is_numeric($update_time) && strlen((string)$update_time) === 10) {
                    $update_time = date('Y-m-d H:i:s',$update_time);
                }
                $bind_type = $user_relation->bind_type;
                systemLog("设备ID:".$data['m_device_id'].'IP::'.Tools::getIp()."已绑定账号,无法注册!");
                throw new MyException('当前设备已绑定账号,无法注册');
                /* if($bind_type == 1){
                    throw new MyException('当前设备在'.$update_time.'已注册账号,请勿重复注册,已注册账号如不是您的账号,请联系客服');
                }else{
                    throw new MyException('当前设备在'.$update_time.'已首次登录账号,无法注册新账号,已登录账号如不是您的账号,请联系客服');
                } */
            }
        }


        $invite_code = $this->generateInviteCode();

        /* $commission = (int)getAppConfig('money','user');
        if($commission>100){
            $commission = 100;
        }
        if($commission<0){
            $commission = 0;
        }
        $admin_commission = (int)getAppConfig('money','admin');
        if($admin_commission>100){
            $admin_commission = 100;
        }
        if($admin_commission<0){
            $admin_commission = 0;
        } */

        $ad_number = (int)getAppConfig('ad','number');
        if($ad_number<0){
            $ad_number = 0;
        }
        if($ad_number>200){
            $ad_number = 200;
        }

        DB::beginTransaction();
        try {
            // 创建用户
            $userData = [
                'pid' => $inviter->id,
                'phone' => $phone,
                'pwd' => md5($pwd),
                'invite_code' => $invite_code,
                'identity' => 2,
                'commission' => 0,
                'user_commission' => 0,
                'admin_commission' => 0,
                'money' => 0,
                'login_time' => time(),
                'add_time' => time(),
                'ad_number' => $ad_number,
                'ip' => Tools::getIp(),
                'login_type' => 0,
                'weixin_image' => '',
                'alipay_image' => '',
                'create_time' => time(),
                'update_time' => time()
            ];
            $user = UserModel::create($userData);

            $adminData = [
                'user_id' => $user->id,
                'username' => $phone,//用手机号登录
                'phone' => $phone,
                'password' => Tools::agentPwd($pwd),
                'real_name' => 'app_'.$user->id,
                'status' => 1,
                'type' => 2,
                'create_time' => time(),
                'update_time' => time()
            ];
            $admin = AdminUserModel::create($adminData);
            $user->admin_id = $admin->id;
            $user->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // 判断是否是唯一索引冲突异常
            if(str_contains($e->getMessage(), 'Duplicate entry') && str_contains($e->getMessage(), 'phone')) {
                throw new MyException('该手机号已被注册,请勿重复提交');
            }
            throw new MyException('注册失败');
        }
        //注册成功后 记录登录信息
        $login_info = [
            'ip' => Tools::getIp(),
            'ip_address' => '',
            'm_type' => $data['m_type'] ?? 1,
            'm_version' => $data['m_version'] ?? '',
            'm_brand' => $data['m_brand'] ?? '',
            'm_model' => $data['m_model'] ?? '',
            'm_device_id' => $data['m_device_id'] ?? '',
            'm_network' => $data['m_network'] ?? 0,
            'm_netserver' => $data['m_netserver'] ?? 0,
            'm_simulator' => $data['m_simulator'] ?? 0,
        ];
        $this->handleLoginRecord($user->id, $login_info);
        $this->handleRegOrLoginRecord($user->id, $admin->id, 1, $login_info);

        $user_info = UserModel::find($user->id)->toArray();

        $pwd = Tools::jsEncrypt($user_info['phone'].':'.$user_info['pwd'],$user_info['pwd']);
        $user_info['token'] = $user_info['phone'].':'.$pwd;
        return $user_info;
    }

    /**
     * 生成测试用的trans_id和sign
     * @return array 包含trans_id和sign的数组
     */
    public function generateTestSign()
    {
        $sign_key = config('ad.gromore.sign_key') ?? '';
        if(empty($sign_key)) {
            throw new MyException('签名密钥未配置');
        }

        // 生成随机trans_id
        $trans_id = Str::random(16);
        $trans_id = '022b8c20-e172-42c9-a4b2-b5ae978164fb';
        // 使用相同的签名算法
        $sign_str = $sign_key . ':' . $trans_id;
        $sign = hash('sha256', $sign_str);

        return [
            'trans_id' => $trans_id,
            'sign' => $sign
        ];
    }

    /**
     * 生成测试用的trans_id和sign
     * @return array 包含trans_id和sign的数组
     */
    public function generateGdtTestSign()
    {
        $sign_key = config('ad.gdt.sign_key') ?? '';
        if(empty($sign_key)) {
            throw new MyException('签名密钥未配置');
        }

        // 生成随机trans_id
        $trans_id = Str::random(16);
        $trans_id = 'ec1f223e4c519920c1b8df836b11ff8d';
        // 使用相同的签名算法
        $sign_str = $trans_id . ':' . $sign_key;
        $sign = hash('sha256', $sign_str);

        return [
            'trans_id' => $trans_id,
            'sign' => $sign
        ];
    }

    public function generateKsTestSign()
    {
        $sign_key = config('ad.ks.sign_key') ?? '';
        if(empty($sign_key)) {
            throw new MyException('签名密钥未配置');
        }
        $trans_id = "2004323118635656322_105804967669_1740565000251";
        $sign_str =  $sign_key . ':' . $trans_id;
        $server_sign = strtolower(md5($sign_str));
        return [
            'trans_id' => $trans_id,
            'sign' => $server_sign
        ];
    }

    public function adRewardInfoByUser($data){
        $secret_key = "FPJ6wdxoXaVygNjW";
        $token = $data['token'] ?? '';
        if(empty($token)){
            throw new MyException('token不能为空');
        }
        $create_time = $data['create_time'] ?? 0;
        $create_time = intval($create_time);
        if($create_time <= 0){
            throw new MyException('create_time不能为空');
        }
        $user_id = $data['user_id'] ?? 0;
        $user_id = intval($user_id);
        $phone = $data['phone'] ?? '';
        if(empty($phone) && $user_id <= 0){
            throw new MyException('手机号或用户ID不能为空');
        }
        if($user_id > 0){
            $user = UserModel::find($user_id);
        }else{
            $user = UserModel::where('phone',$phone)->first();
        }
        if(!$user){
            throw new MyException('用户不存在');
        }
        if(!is_array($user)){
            $user = $user->toArray();
        }
        $phone = $user['phone'];
        if(md5($phone.$secret_key) != $token){
            throw new MyException('token错误');
        }

        $reward_ad_num = $ad_reward_config['reward_ad_num'] ?? 0;
        $reward_ad_num = intval($reward_ad_num);
        if($reward_ad_num <= 0){
            $reward_ad_num = 30;
        }

        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($user['pid'], $system_tg_code_arr)){
            $is_system_tg = 1;
        }

        if ($is_system_tg == 0) {
            return [
                'reward_open' => 1,
                'is_system_tg' => 0,
                'reward_ad_num' => $reward_ad_num,
                'user_ad_number_today' => 0,
                'user_ad_income_today' => 0,
                'user_withdraw_money_success_today' => 0,
                'user_withdraw_money_auditing_today' => 0,
                'user_withdraw_reward_money_success_today' => 0,
                'user_withdraw_reward_money_auditing_today' => 0,
                'max_ad_reward_money' => 0,
                'reward_latest_time' => 0,
                'count_next_number' => 0,
                'user_ad_number_yesterday' => 0,
                'user_ad_income_yesterday' => 0,
                'user_withdraw_money_success_yesterday' => 0,
                'remain_reward_money_yesterday' => 0,
                'remain_reward_money_current' => 0,
                'latest_cal_time' => 0,
                'user_info' => $user
            ];
        }

        $today = date('Y-m-d',$create_time);
        $yesterday = date('Y-m-d',strtotime('-1 day',$create_time));

        $user_ad_number_today = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$today)->count();
        $user_ad_income_today = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$today)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_today = floatval(format_money($user_ad_income_today,1));

        $user_withdraw_money_success_today = Withdraw::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_success_today = floatval(format_money($user_withdraw_money_success_today,1));

        $user_withdraw_money_auditing_today = Withdraw::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_today = floatval(format_money($user_withdraw_money_auditing_today,1));

        $user_withdraw_reward_money_success_today = WithdrawReward::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_success_today = floatval(format_money($user_withdraw_reward_money_success_today,1));

        $user_withdraw_reward_money_auditing_today = WithdrawReward::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_auditing_today = floatval(format_money($user_withdraw_reward_money_auditing_today,1));

        $max_ad_reward_money = min($user_ad_income_today, $user_withdraw_money_success_today);

        $reward_latest_time = WithdrawReward::query()->where('user_id',$user_id)->where('status','<',3)->orderBy('id','desc')->value('create_time');
        if(!empty($reward_latest_time) && !is_numeric($reward_latest_time)){
            $reward_latest_time = strtotime($reward_latest_time);
        }

        $count_next_number = 0;
        $count_next_reward_time = 0;
        $count_next_reward_time_f = "";
        if(empty($reward_latest_time)){
            $reward_latest_time = 0;
        }else{
            $count_next_number = 7200 - (time() - $reward_latest_time);
            if($count_next_number <= 0){
                $count_next_number = 0;
            }else{
                $count_next_reward_time = $reward_latest_time + 7200;
                $count_next_reward_time_f = date('Y-m-d H:i:s',$count_next_reward_time);
            }
        }

        $user_ad_number_yesterday = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$yesterday)->count();
        $user_ad_income_yesterday = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$yesterday)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_yesterday = floatval(format_money($user_ad_income_yesterday,1));

        $user_withdraw_money_success_yesterday = Withdraw::query()->where('user_id',$user_id)->where('add_time',$yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_success_yesterday = floatval(format_money($user_withdraw_money_success_yesterday,1));

        $user_withdraw_money_auditing_yesterday = Withdraw::query()->where('user_id',$user_id)->where('add_time',$yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_yesterday = floatval(format_money($user_withdraw_money_auditing_yesterday,1));

        $user_withdraw_reward_money_success_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_auditing_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_reward_money_success_yesterday = floatval(format_money($user_withdraw_reward_money_success_yesterday,1));
        $user_withdraw_reward_money_auditing_yesterday = floatval(format_money($user_withdraw_reward_money_auditing_yesterday,1));


        $max_ad_reward_money_yesterday = min($user_ad_income_yesterday, $user_withdraw_money_success_yesterday);

        if($user_ad_number_yesterday <= $reward_ad_num){
            $remain_reward_money_yesterday = 0;
        }else{
            $remain_reward_money_yesterday = $max_ad_reward_money_yesterday - $user_withdraw_reward_money_success_yesterday - $user_withdraw_reward_money_auditing_yesterday;
            $remain_reward_money_yesterday = floatval(format_money($remain_reward_money_yesterday,1));
        }

        $remain_reward_money_today = $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        $remain_reward_money_today = floatval(format_money($remain_reward_money_today,1));

        $remain_reward_money_current = $remain_reward_money_yesterday + $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;

        $remain_reward_money_current = floatval(format_money($remain_reward_money_current,1));


        $user_remind_withdraw_money = $user_ad_income_today - $user_withdraw_money_success_today - $user_withdraw_money_auditing_today;
        $user_remind_withdraw_money = floatval(format_money($user_remind_withdraw_money,1));
        if($user_remind_withdraw_money <= 0){
            $user_remind_withdraw_money = 0;
        }


        return [
            'reward_open' => 1,
            'is_system_tg' => 1,
            'reward_ad_num' => $reward_ad_num,
            'user_ad_number_today' => $user_ad_number_today,
            'user_ad_income_today' => $user_ad_income_today,
            'user_withdraw_money_success_today' => $user_withdraw_money_success_today,
            'user_withdraw_money_auditing_today' => $user_withdraw_money_auditing_today,
            'user_withdraw_reward_money_success_today' => $user_withdraw_reward_money_success_today,
            'user_withdraw_reward_money_auditing_today' => $user_withdraw_reward_money_auditing_today,
            'max_ad_reward_money' => $max_ad_reward_money,
            'reward_latest_time' => !empty($reward_latest_time) ? date('Y-m-d H:i:s',$reward_latest_time) : '',
            'count_next_number' => $count_next_number,
            'count_next_reward_time' => $count_next_reward_time,
            'count_next_reward_time_f' => $count_next_reward_time_f,
            'user_ad_number_yesterday' => $user_ad_number_yesterday ?? 0,
            'user_ad_income_yesterday' => $user_ad_income_yesterday ?? 0,
            'user_withdraw_money_success_yesterday' => $user_withdraw_money_success_yesterday ?? 0,
            'user_withdraw_money_auditing_yesterday' => $user_withdraw_money_auditing_yesterday ?? 0,
            'user_withdraw_reward_money_success_yesterday' => $user_withdraw_reward_money_success_yesterday ?? 0,
            'user_withdraw_reward_money_auditing_yesterday' => $user_withdraw_reward_money_auditing_yesterday ?? 0,
            'remain_reward_money_yesterday' => $remain_reward_money_yesterday,
            'remain_reward_money_current' => $remain_reward_money_current>0 ? $remain_reward_money_current : 0,
            'user_remind_withdraw_money' => $user_remind_withdraw_money,
            'user_info' => $user
        ];





    }

    public function adReward($user){
        $user_id = $user['id'];

        $ad_reward_config = AppConfig::query()->where('type','withdrawal_reward')->select(['title','value'])->get()->toArray();
        $ad_reward_config = array_column($ad_reward_config, 'value', 'title');

        $reward_open = intval($ad_reward_config['reward_open'] ?? 0);
        if($reward_open != 1){
            $reward_open = 0;
        }

        $site_name = config('ad.system_name');


        $ad_reward_desc = $ad_reward_config['ad_reward_desc'] ?? '';
        if(!empty($ad_reward_desc)){
            // 先按换行符分割
            $items = array_filter(explode("\r\n", $ad_reward_desc), function($item) {
                return !empty(trim($item));
            });

            // 如果只有一个元素，且包含"数字+顿号"格式，则按该格式分割
            if(count($items) === 1 && preg_match('/\d+、/', $items[0])) {
                $items = preg_split('/\d+、/', $items[0], -1, PREG_SPLIT_NO_EMPTY);
                $items = array_map('trim', $items);
            }

            $ad_reward_desc = array_values(array_filter($items));
        }

        $ad_reward_close_desc = $ad_reward_config['ad_reward_close_desc'] ?? '';
        if(empty($ad_reward_close_desc)){
            $ad_reward_close_desc = "";
        }

        $reward_ad_num = $ad_reward_config['reward_ad_num'] ?? 0;
        $reward_ad_num = intval($reward_ad_num);
        if($reward_ad_num <= 0){
            $reward_ad_num = 30;
        }

        $ad_reward_down_desc = $ad_reward_config['ad_reward_down_desc'] ?? '';
        if(empty($ad_reward_down_desc)){
            $ad_reward_down_desc = "";
        }
        $ad_reward_down_desc = str_ireplace('{num}',$reward_ad_num,$ad_reward_down_desc);

        $ad_reward_success_desc = $ad_reward_config['ad_reward_success_desc'] ?? '';
        if(empty($ad_reward_success_desc)){
            $ad_reward_success_desc = "";
        }


        if ($reward_open == 0) {
            return [
                'reward_open' => 0,
                'is_system_tg' => 0,
                'reward_ad_num' => $reward_ad_num,
                'ad_reward_desc' => $ad_reward_desc,
                'ad_reward_close_desc' => $ad_reward_close_desc,
                'ad_reward_down_desc' => $ad_reward_down_desc,
                'ad_reward_success_desc' => $ad_reward_success_desc,
                'user_ad_number_today' => 0,
                'user_ad_income_today' => 0,
                'user_withdraw_money_success_today' => 0,
                'user_withdraw_money_auditing_today' => 0,
                'user_withdraw_reward_money_success_today' => 0,
                'user_withdraw_reward_money_auditing_today' => 0,
                'max_ad_reward_money' => 0,
                'reward_latest_time' => 0,
                'count_next_number' => 0,
                'user_ad_number_yesterday' => 0,
                'user_ad_income_yesterday' => 0,
                'user_withdraw_money_success_yesterday' => 0,
                'remain_reward_money_yesterday' => 0,
                'remain_reward_money_current' => 0,
                'latest_cal_time' => 0,
                'site_name' => $site_name,
                'user_info' => $user
            ];
        }

        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($user['pid'], $system_tg_code_arr)){
            $is_system_tg = 1;
        }

        if ($is_system_tg == 0) {
            return [
                'reward_open' => 1,
                'is_system_tg' => 0,
                'reward_ad_num' => $reward_ad_num,
                'ad_reward_desc' => $ad_reward_desc,
                'ad_reward_close_desc' => $ad_reward_close_desc,
                'ad_reward_down_desc' => $ad_reward_down_desc,
                'ad_reward_success_desc' => $ad_reward_success_desc,
                'user_ad_number_today' => 0,
                'user_ad_income_today' => 0,
                'user_withdraw_money_success_today' => 0,
                'user_withdraw_money_auditing_today' => 0,
                'user_withdraw_reward_money_success_today' => 0,
                'user_withdraw_reward_money_auditing_today' => 0,
                'max_ad_reward_money' => 0,
                'reward_latest_time' => 0,
                'count_next_number' => 0,
                'user_ad_number_yesterday' => 0,
                'user_ad_income_yesterday' => 0,
                'user_withdraw_money_success_yesterday' => 0,
                'remain_reward_money_yesterday' => 0,
                'remain_reward_money_current' => 0,
                'latest_cal_time' => 0,
                'site_name' => $site_name,
                'user_info' => $user
            ];
        }




        $today = date('Y-m-d');

        $now = Carbon::now();
        $yesterday = $now->subDay()->format('Y-m-d');

        $user_ad_number_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->count();


        $user_ad_income_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_today = floatval(format_money($user_ad_income_today,1));

        $user_withdraw_money_success_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_money_success_today = floatval(format_money($user_withdraw_money_success_today,1));
        $user_withdraw_money_auditing_today = floatval(format_money($user_withdraw_money_auditing_today,1));


        $user_withdraw_reward_money_success_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_auditing_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_reward_money_success_today = floatval(format_money($user_withdraw_reward_money_success_today,1));
        $user_withdraw_reward_money_auditing_today = floatval(format_money($user_withdraw_reward_money_auditing_today,1));

        #实时的最大可提现金额 允许用户直接操作提现的数额
        $max_ad_reward_money = min($user_ad_income_today, $user_withdraw_money_success_today);
        if($user_ad_number_today < $reward_ad_num){
            //没看够要求的广告数量
            $max_ad_reward_money = 0;
        }

        /* var_dump("今日广告数量:".$user_ad_number_today);
        var_dump("今日广告收益:".$user_ad_income_today);
        var_dump("今日提现成功金额:".$user_withdraw_money_success_today);
        var_dump("今日提现审核中金额:".$user_withdraw_money_auditing_today);
        var_dump("今日奖励提现成功金额:".$user_withdraw_reward_money_success_today);
        var_dump("今日奖励提现审核中金额:".$user_withdraw_reward_money_auditing_today);
        var_dump("今日最大可提现金额:".$max_ad_reward_money); */

        #最近一次申请领取红包的时间
        $reward_latest_time = WithdrawReward::query()->where('user_id', $user_id)->where('status','<',3)->orderBy('id', 'desc')->value('create_time');
        if(!empty($reward_latest_time) && !is_numeric($reward_latest_time)){
            $reward_latest_time = strtotime($reward_latest_time);
        }
        $count_next_number = 0; #7200秒后可以再次领取红包
        $count_next_reward_time = 0; #7200秒后可以再次领取红包
        $count_next_reward_time_f = "";
        if(empty($reward_latest_time)){
            $reward_latest_time = 0;
        }else{
            $count_next_number = 7200 - (time() - $reward_latest_time);
            if($count_next_number <= 0){
                $count_next_number = 0;
            }else{
                $count_next_reward_time = $reward_latest_time + 7200;
                $count_next_reward_time_f = date('Y-m-d H:i:s',$count_next_reward_time);
            }
        }

        //var_dump("7200秒后可以再次领取红包:".$count_next_number);

        //var_dump("昨日日期:".$yesterday);
        #计算昨日剩余可提现金额
        $user_ad_number_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->count();

        $user_ad_income_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_yesterday = floatval(format_money($user_ad_income_yesterday,1));

        $user_withdraw_money_success_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_money_success_yesterday = floatval(format_money($user_withdraw_money_success_yesterday,1));
        $user_withdraw_money_auditing_yesterday = floatval(format_money($user_withdraw_money_auditing_yesterday,1));

        $user_withdraw_reward_money_success_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_auditing_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_reward_money_success_yesterday = floatval(format_money($user_withdraw_reward_money_success_yesterday,1));
        $user_withdraw_reward_money_auditing_yesterday = floatval(format_money($user_withdraw_reward_money_auditing_yesterday,1));


        $max_ad_reward_money_yesterday = min($user_ad_income_yesterday, $user_withdraw_money_success_yesterday);



        //var_dump("昨日广告数量:".$user_ad_number_yesterday);
        if($user_ad_number_yesterday <= $reward_ad_num){
            #没看够要求的广告数量
            $remain_reward_money_yesterday = 0;
            //var_dump("昨日剩余可提现金额[广告数量不达标]:".$remain_reward_money_yesterday);
        }else{

            $remain_reward_money_yesterday = $max_ad_reward_money_yesterday - $user_withdraw_reward_money_success_yesterday - $user_withdraw_reward_money_auditing_yesterday;
            $remain_reward_money_yesterday = floatval(format_money($remain_reward_money_yesterday,1));
            if( $remain_reward_money_yesterday <= 0){
                $remain_reward_money_yesterday = 0;
            }
        }
        //var_dump("昨日广告奖励金额:".$remain_reward_money_yesterday);

        $remain_reward_money_today = $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        $remain_reward_money_today = floatval(format_money($remain_reward_money_today,1));

        $remain_reward_money_current = $remain_reward_money_yesterday + $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        //var_dump("当前可获取广告奖励金额:".$remain_reward_money_current);
        $remain_reward_money_current = floatval(format_money($remain_reward_money_current,1));


        $user_remind_withdraw_money = $user_ad_income_today - $user_withdraw_money_success_today - $user_withdraw_money_auditing_today;
        $user_remind_withdraw_money = floatval(format_money($user_remind_withdraw_money,1));
        if($user_remind_withdraw_money <= 0){
            $user_remind_withdraw_money = 0;
        }

        $latest_cal_time = time();
        $user_ad_info = UserAdReward::query()->where('user_id',$user_id)->first();
        if($user_ad_info){
            UserAdReward::query()->where('user_id',$user_id)
                                ->update([
                                    'latest_cal_time'   => $latest_cal_time,
                                    'add_time'          => date("Y-m-d H:i:s"),
                                    'update_time'       => time()
                                ]);
        }else{
            UserAdReward::query()->insert([
                'user_id'           => $user_id,
                'ad_reward_num'     => 0,
                'latest_cal_time'   => $latest_cal_time,
                'add_time'          => date("Y-m-d H:i:s"),
                'update_time'       => time(),
                'create_time'       => time()
            ]);
        }

        return [
            'reward_open' => 1,
            'is_system_tg' => 1,
            'reward_ad_num' => $reward_ad_num,
            'ad_reward_desc' => $ad_reward_desc,
            'ad_reward_close_desc' => $ad_reward_close_desc,
            'ad_reward_down_desc' => $ad_reward_down_desc,
            'ad_reward_success_desc' => $ad_reward_success_desc,
            'user_ad_number_today' => $user_ad_number_today,
            'user_ad_income_today' => $user_ad_income_today,
            'user_withdraw_money_success_today' => $user_withdraw_money_success_today,
            'user_withdraw_money_auditing_today' => $user_withdraw_money_auditing_today,
            'user_withdraw_reward_money_success_today' => $user_withdraw_reward_money_success_today,
            'user_withdraw_reward_money_auditing_today' => $user_withdraw_reward_money_auditing_today,
            'max_ad_reward_money' => $max_ad_reward_money,
            'reward_latest_time' => !empty($reward_latest_time) ? date('Y-m-d H:i:s',$reward_latest_time) : '',
            'count_next_number' => $count_next_number,
            'count_next_reward_time' => $count_next_reward_time,
            'count_next_reward_time_f' => $count_next_reward_time_f,
            'user_ad_number_yesterday' => $user_ad_number_yesterday ?? 0,
            'user_ad_income_yesterday' => $user_ad_income_yesterday ?? 0,
            'user_withdraw_money_success_yesterday' => $user_withdraw_money_success_yesterday ?? 0,
            'user_withdraw_money_auditing_yesterday' => $user_withdraw_money_auditing_yesterday ?? 0,
            'user_withdraw_reward_money_success_yesterday' => $user_withdraw_reward_money_success_yesterday ?? 0,
            'user_withdraw_reward_money_auditing_yesterday' => $user_withdraw_reward_money_auditing_yesterday ?? 0,
            'remain_reward_money_yesterday' => $remain_reward_money_yesterday,
            'remain_reward_money_current' => $remain_reward_money_current>0 ? $remain_reward_money_current : 0,
            'user_remind_withdraw_money' => $user_remind_withdraw_money,
            'latest_cal_time' => $latest_cal_time,
            'site_name' => $site_name,
            'user_info' => $user
        ];

    }

    public function adRewardAdd($data){
        $user_id = $data['user_id'];

        if(empty($data['weixin_image'])){
            throw new MyException('请先上传收款码');
        }
        // 添加并发控制
        $key = "ad_reward_add_limit:{$user_id}";
        if (!Cache::add($key, now(), 15)) {
            throw new MyException('广告奖励提交过于频繁，请稍候再试');
        }

        $pid = $data['pid'];
        $ad_reward_config = AppConfig::query()->where('type','withdrawal_reward')->select(['title','value'])->get()->toArray();
        $ad_reward_config = array_column($ad_reward_config, 'value', 'title');

        $reward_open = intval($ad_reward_config['reward_open'] ?? 0);
        if($reward_open != 1){
            $reward_open = 0;
        }

        if($reward_open == 0){
            throw new MyException('广告奖励活动已结束');
        }

        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($pid, $system_tg_code_arr)){
            $is_system_tg = 1;
        }

        if($is_system_tg == 0){
            throw new MyException('您不符合参加广告奖励活动的资格');
        }

        $reward_ad_num = $ad_reward_config['reward_ad_num'] ?? 0;
        $reward_ad_num = intval($reward_ad_num);
        if($reward_ad_num <= 0){
            $reward_ad_num = 30;
        }

        $latest_cal_time = $data['latest_cal_time'] ?? 0;
        if($latest_cal_time <= 0){
            throw new MyException('您提交的广告奖励数据不合法');
        }

        if(date('Y-m-d',$latest_cal_time) != date('Y-m-d')){
            throw new MyException('当前日期已经更改,请重新进入该页面再次提交');
        }

        $user_ad_info = UserAdReward::query()->where('user_id',$user_id)->first();
        if(!$user_ad_info || $user_ad_info->latest_cal_time == 0){
            throw new MyException('请先浏览广告奖励页面');
        }

        if($user_ad_info->latest_cal_time != $latest_cal_time){
            throw new MyException('您提交的广告奖励数据不合法');
        }

        $reward_latest_time = WithdrawReward::query()->where('user_id', $user_id)->where('status','<',3)->orderBy('id', 'desc')->value('create_time');
        if(!empty($reward_latest_time) && !is_numeric($reward_latest_time)){
            $reward_latest_time = strtotime($reward_latest_time);
        }

        if(!empty($reward_latest_time)){
            $count_next_number = 7200 - (time() - $reward_latest_time);
            if($count_next_number > 0){
                throw new MyException('距离上次领取广告奖励时间不足2小时');
            }
        }

        $today = date('Y-m-d');
        $now = Carbon::now();
        $yesterday = $now->subDay()->format('Y-m-d');

        $user_ad_number_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->count();


        $user_ad_income_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_today = floatval(format_money($user_ad_income_today,1));

        $user_withdraw_money_success_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_money_success_today = floatval(format_money($user_withdraw_money_success_today,1));
        $user_withdraw_money_auditing_today = floatval(format_money($user_withdraw_money_auditing_today,1));


        $user_withdraw_reward_money_success_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_auditing_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_reward_money_success_today = floatval(format_money($user_withdraw_reward_money_success_today,1));
        $user_withdraw_reward_money_auditing_today = floatval(format_money($user_withdraw_reward_money_auditing_today,1));

        #实时的最大可提现金额 允许用户直接操作提现的数额
        $max_ad_reward_money = min($user_ad_income_today, $user_withdraw_money_success_today);
        if($user_ad_number_today < $reward_ad_num){
            //没看够要求的广告数量
            $max_ad_reward_money = 0;
        }

        //var_dump("昨日日期:".$yesterday);
        #计算昨日剩余可提现金额
        $user_ad_number_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->count();
        //var_dump("昨日广告数量:".$user_ad_number_yesterday);
        if($user_ad_number_yesterday <= $reward_ad_num){
            #没看够要求的广告数量
            $remain_reward_money_yesterday = 0;
            //var_dump("昨日剩余可提现金额[广告数量不达标]:".$remain_reward_money_yesterday);
        }else{
            $user_ad_income_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
            $user_ad_income_yesterday = floatval(format_money($user_ad_income_yesterday,1));

            $user_withdraw_money_success_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
            //$user_withdraw_money_auditing_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

            $user_withdraw_money_success_yesterday = floatval(format_money($user_withdraw_money_success_yesterday,1));
            //$user_withdraw_money_auditing_yesterday = floatval(format_money($user_withdraw_money_auditing_yesterday,1));

            $user_withdraw_reward_money_success_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
            $user_withdraw_reward_money_auditing_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

            $user_withdraw_reward_money_success_yesterday = floatval(format_money($user_withdraw_reward_money_success_yesterday,1));
            $user_withdraw_reward_money_auditing_yesterday = floatval(format_money($user_withdraw_reward_money_auditing_yesterday,1));

            $max_ad_reward_money_yesterday = min($user_ad_income_yesterday, $user_withdraw_money_success_yesterday);

            $remain_reward_money_yesterday = $max_ad_reward_money_yesterday - $user_withdraw_reward_money_success_yesterday - $user_withdraw_reward_money_auditing_yesterday;
            $remain_reward_money_yesterday = floatval(format_money($remain_reward_money_yesterday,1));

            if( $remain_reward_money_yesterday <= 0){
                $remain_reward_money_yesterday = 0;
            }

        }

        $remain_reward_money_today = $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        $remain_reward_money_today = floatval(format_money($remain_reward_money_today,1));

        $remain_reward_money_current = $remain_reward_money_yesterday + $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        //var_dump("当前可获取广告奖励金额:".$remain_reward_money_current);
        $remain_reward_money_current = floatval(format_money($remain_reward_money_current,1));

        $user_submit_ad_reward_money = $data['user_submit_ad_reward_money'] ?? 0;
        $user_submit_ad_reward_money = floatval(format_money($user_submit_ad_reward_money,1));
        if($user_submit_ad_reward_money <= 0){
            throw new MyException('您提交的广告奖励数据不合法');
        }

        if($user_submit_ad_reward_money != $remain_reward_money_current){
            throw new MyException('广告奖励数据已发生变化，请重新进入该页面再次提交');
        }

        DB::beginTransaction();
        try{
            $user_ad_info->ad_reward_num = $user_ad_info->ad_reward_num + $user_submit_ad_reward_money;
            $user_ad_info->save();
            UserAdReward::query()->where('user_id',$user_id)
                                    ->update([
                                        'latest_cal_time'   => 0, //重置计算时间
                                        'add_time'          => date("Y-m-d H:i:s"),
                                        'update_time'       => time()
                                    ]);

            $withdraw_id = WithdrawReward::query()->insertGetId([
                'user_id'           => $user_id,
                'real_money'        => $user_submit_ad_reward_money,
                'add_time'          => date("Y-m-d H:i:s"),
                'status'            => 1,
                'reply'             => '',
                'type'              => 1,
                'add_time'          => date("Y-m-d"),
                'update_time'       => time(),
                'create_time'       => time(),
                'delete_time'       => 0
            ]);

            if(config('ad.push_to_third')==1){
                // 调用封装的方法推送数据到第三方
                $user = UserModel::where('id', $user_id)->first();
                $this->pushWithdrawDataToThird(11, $user, $user_submit_ad_reward_money, $withdraw_id);
            }

            DB::commit();
            return ['status'=>1,'msg'=>'提交成功'];
        }catch(\Exception $e){
            DB::rollBack();
            throw new MyException('广告奖励数据提交失败');
        }
    }


    public function adRewardList($data,$page,$page_size){
        $user_id = $data['user_id'];
        $status = $data['status'] ?? 0;
        $list = WithdrawReward::query()->where('user_id',$user_id)
                            ->when((int)$status != 0,function($query) use ($status){
                                return $query->where('status',$status);
                            })
                            ->orderBy('id','desc')
                            ->paginate($page_size,['*'],'page',$page);
        $list = $list->toArray();
        $list['data'] = array_map(function($item){
            $item['real_money'] = format_money($item['real_money'],1);
            if($item['status'] == 1){
                $item['status_text'] = '审核中';
            }else if($item['status'] == 2){
                $item['status_text'] = '已到账';
            }else{
                $item['status_text'] = '已驳回';
            }
            $item['show_time'] = $item['status'] == 2 ? $item['adopt_time'] : $item['create_time'];
            return $item;
        },$list['data']);
        return $list;
    }

}

