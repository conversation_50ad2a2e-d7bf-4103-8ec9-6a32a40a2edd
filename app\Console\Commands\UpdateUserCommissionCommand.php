<?php
declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\User\User;
use Illuminate\Console\Command;

class UpdateUserCommissionCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'user:update-commission';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '更新用户commission字段到user_commission字段';

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            $users = User::all();
            $count = 0;

            foreach ($users as $user) {
                $commission = (int)$user->commission;
                if($user->pid > 0){
                    $user->user_commission = $commission;
                    // 如果commission大于25，则设置为25
                    if ($commission > 25) {
                        $commission = 25;
                    }
                }
                $user->commission = $commission;
                if ($user->save()) {
                    $count++;
                }
            }

            $this->info("成功更新 {$count} 个用户的commission数据");

        } catch (\Exception $e) {
            $this->error("更新用户commission数据时发生错误: " . $e->getMessage());
        }
    }
} 