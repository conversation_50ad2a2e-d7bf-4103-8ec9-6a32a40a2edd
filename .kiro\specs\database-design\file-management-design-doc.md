# 文件管理系统设计文档

## 概述

文件管理系统是次推应用的核心基础设施，负责处理用户上传的各种文件，包括APP图标、评测截图、线索截图、用户头像等。系统设计遵循安全性、可扩展性和高性能原则。

## 业务需求分析

### 核心功能需求
1. **文件上传管理**：支持多种格式文件上传，记录完整的文件元数据
2. **文件分类管理**：按业务类型对文件进行分类，便于管理和查询
3. **文件关联管理**：建立文件与业务对象的灵活关联关系
4. **存储策略支持**：支持本地存储和云存储多种方式
5. **安全访问控制**：支持公开和私有文件的访问控制

### 业务场景
- **APP图标上传**：用户提交APP评测时上传应用图标
- **截图管理**：评测报告和线索提交时的截图文件
- **头像管理**：用户个人头像的上传和更新
- **附件支持**：支持文档等其他类型文件的上传

## 表结构设计

### 1. 文件分类表 (ct_file_categories)

#### 设计目标
- 提供灵活的文件分类体系
- 支持层级分类结构
- 控制不同类型文件的上传限制

#### 核心字段
- `category_id`: 分类主键，自增ID
- `category_name`: 分类名称，用于显示
- `category_code`: 分类代码，用于程序识别
- `parent_id`: 父分类ID，支持层级结构
- `max_file_size`: 该分类允许的最大文件大小
- `allowed_extensions`: 允许的文件扩展名列表

#### 业务规则
- 支持无限层级的分类结构
- 每个分类可设置独立的文件大小和格式限制
- 分类可以启用/禁用状态控制

### 2. 文件信息表 (ct_files)

#### 设计目标
- 存储完整的文件元数据信息
- 支持多种存储方式
- 提供文件去重和安全控制

#### 核心字段
- `file_id`: 文件主键，使用BIGINT支持大量文件
- `original_name`: 用户上传时的原始文件名
- `stored_name`: 系统存储的文件名（UUID）
- `file_path`: 文件在存储系统中的路径
- `file_url`: 文件的访问URL
- `file_hash`: 文件MD5哈希值，用于去重
- `width/height`: 图片尺寸信息
- `storage_type`: 存储类型（本地/云存储）

#### 特殊设计
- **软删除机制**：使用`is_deleted`字段实现软删除
- **文件去重**：通过`file_hash`字段避免重复存储
- **多存储支持**：`storage_type`字段支持多种存储方式
- **访问控制**：`is_public`字段控制文件访问权限

### 3. 文件关联表 (ct_file_relations)

#### 设计目标
- 建立文件与业务对象的灵活关联
- 支持一对多和多对多关联关系
- 提供关联类型和排序功能

#### 核心字段
- `file_id`: 关联的文件ID
- `business_type`: 业务类型（如app_logo, evaluation_screenshot）
- `business_id`: 业务对象ID
- `relation_type`: 关联类型（附件、封面、缩略图等）
- `is_primary`: 是否为主要文件标识

#### 关联类型设计
- **app_logo**: APP图标文件
- **evaluation_screenshot**: 评测截图
- **clue_screenshot**: 线索截图
- **user_avatar**: 用户头像
- **system_image**: 系统图片

## 索引设计策略

### 主要索引
1. **分类查询索引**：`idx_category_code`, `idx_parent_id`
2. **文件查询索引**：`idx_uploader_id`, `idx_file_hash`, `idx_file_type`
3. **关联查询索引**：`idx_business_type_id`, `idx_file_business`

### 复合索引
- `idx_uploader_category`: 支持按用户和分类查询文件
- `idx_deleted_created`: 支持按删除状态和时间查询
- `idx_business_primary`: 快速查找业务对象的主要文件

## 约束设计

### 外键约束
- 文件分类的父子关系约束
- 文件与用户的关联约束
- 文件关联的级联删除约束

### 唯一性约束
- 分类代码唯一性
- 文件业务关联的唯一性

### 检查约束
- 文件大小合理性检查
- 状态字段值域检查

## 触发器设计

### 软删除触发器
- 自动设置删除时间戳
- 清理相关联的业务关系

### 数据一致性触发器
- 确保文件关联关系的完整性
- 自动清理无效的关联记录

## 性能优化策略

### 查询优化
1. **分页查询优化**：使用覆盖索引减少回表
2. **文件列表查询**：预加载关联的分类信息
3. **业务关联查询**：使用复合索引提升查询效率

### 存储优化
1. **文件去重**：通过哈希值避免重复存储
2. **分区策略**：按时间或大小对大表进行分区
3. **归档策略**：定期归档历史文件数据

## 安全设计

### 访问控制
- 基于`is_public`字段的访问权限控制
- 用户只能访问自己上传的私有文件
- 管理员可以访问所有文件

### 文件安全
- 文件类型白名单验证
- 文件大小限制
- 恶意文件检测和隔离

### 数据安全
- 敏感文件路径加密存储
- 文件访问日志记录
- 定期安全审计

## 扩展性设计

### 存储扩展
- 支持多种云存储服务
- 存储策略可配置化
- 支持CDN加速

### 功能扩展
- 图片处理服务集成
- 文件版本管理
- 文件分享和权限管理

### 性能扩展
- 读写分离支持
- 缓存策略优化
- 异步处理支持

## 监控和维护

### 监控指标
- 文件上传成功率
- 存储空间使用情况
- 文件访问频率统计
- 系统性能指标

### 维护策略
- 定期清理软删除文件
- 存储空间监控和告警
- 文件完整性校验
- 性能调优和索引优化