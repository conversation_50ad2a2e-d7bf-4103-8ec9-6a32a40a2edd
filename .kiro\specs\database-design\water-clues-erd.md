# 放水线索相关表实体关系图 (ERD)

## 概述

本文档展示了放水线索管理系统中各表之间的实体关系，包括主要的外键关联、数据流向和业务关系。

## 实体关系图

```mermaid
erDiagram
    ct_users ||--o{ ct_water_clues : "提交"
    ct_admin_users ||--o{ ct_water_clues : "审核"
    ct_apps ||--o{ ct_water_clues : "关联"
    ct_water_clues ||--o{ ct_clue_feedbacks : "反馈"
    ct_water_clues ||--o{ ct_clue_statistics : "统计"
    ct_users ||--o{ ct_clue_feedbacks : "提交反馈"
    ct_admin_users ||--o{ ct_clue_feedbacks : "审核反馈"

    ct_users {
        bigint id PK "用户ID"
        varchar phone UK "手机号"
        varchar nick_name "昵称"
        varchar avatar "头像"
        int total_points "总积分"
        tinyint status "状态"
        datetime created_at "创建时间"
    }

    ct_admin_users {
        bigint id PK "管理员ID"
        varchar username UK "用户名"
        varchar real_name "真实姓名"
        tinyint role_type "角色类型"
        tinyint status "状态"
        datetime created_at "创建时间"
    }

    ct_apps {
        int id PK "APP ID"
        varchar name "APP名称"
        int category_id "分类ID"
        varchar logo_url "Logo URL"
        decimal rating "平均评分"
        tinyint status "状态"
        datetime create_time "创建时间"
    }

    ct_water_clues {
        bigint id PK "线索ID"
        int app_id FK "APP ID"
        bigint user_id FK "用户ID"
        varchar title "标题"
        decimal water_amount "放水金额"
        tinyint water_type "放水类型"
        text description "描述"
        datetime water_time "放水时间"
        int participant_count "参与人数"
        decimal success_rate "成功率"
        tinyint verification_status "验证状态"
        tinyint audit_status "审核状态"
        bigint audit_user_id FK "审核人ID"
        int view_count "查看次数"
        int like_count "点赞次数"
        decimal hot_score "热度分数"
        tinyint is_hot "是否热门"
        tinyint status "状态"
        datetime created_at "创建时间"
    }

    ct_clue_feedbacks {
        bigint id PK "反馈ID"
        bigint clue_id FK "线索ID"
        bigint user_id FK "用户ID"
        tinyint feedback_type "反馈类型"
        tinyint is_successful "是否成功"
        decimal actual_amount "实际金额"
        int time_spent "花费时间"
        tinyint difficulty_rating "难度评分"
        tinyint overall_rating "总体评分"
        text feedback_content "反馈内容"
        varchar device_info "设备信息"
        tinyint audit_status "审核状态"
        bigint audit_user_id FK "审核人ID"
        int helpful_count "有用评价数"
        tinyint status "状态"
        datetime created_at "创建时间"
    }

    ct_clue_statistics {
        bigint id PK "统计ID"
        bigint clue_id FK "线索ID"
        date stat_date "统计日期"
        tinyint stat_type "统计类型"
        int view_count "查看次数"
        int like_count "点赞次数"
        int feedback_count "反馈次数"
        int success_feedback_count "成功反馈次数"
        decimal success_rate "成功率"
        decimal average_rating "平均评分"
        decimal hot_score "热度分数"
        int rank_position "排名位置"
        datetime created_at "创建时间"
    }
```

## 关系说明

### 1. 用户与线索关系 (ct_users → ct_water_clues)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_water_clues.user_id` → `ct_users.id`
- **业务含义**: 一个用户可以提交多个放水线索
- **约束**: CASCADE DELETE - 用户删除时，其提交的线索也会被删除
- **索引**: `idx_user_id` 支持查询用户的所有线索

### 2. 管理员与线索审核关系 (ct_admin_users → ct_water_clues)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_water_clues.audit_user_id` → `ct_admin_users.id`
- **业务含义**: 一个管理员可以审核多个线索
- **约束**: SET NULL - 管理员删除时，审核记录保留但审核人置空
- **索引**: 支持查询管理员的审核历史

### 3. APP与线索关系 (ct_apps → ct_water_clues)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_water_clues.app_id` → `ct_apps.id`
- **业务含义**: 一个APP可以有多个放水线索
- **约束**: CASCADE DELETE - APP删除时，相关线索也会被删除
- **索引**: `idx_app_id` 支持按APP筛选线索

### 4. 线索与反馈关系 (ct_water_clues → ct_clue_feedbacks)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_clue_feedbacks.clue_id` → `ct_water_clues.id`
- **业务含义**: 一个线索可以收到多个用户反馈
- **约束**: CASCADE DELETE - 线索删除时，相关反馈也会被删除
- **索引**: `idx_clue_id` 支持查询线索的所有反馈

### 5. 用户与反馈关系 (ct_users → ct_clue_feedbacks)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_clue_feedbacks.user_id` → `ct_users.id`
- **业务含义**: 一个用户可以提交多个反馈
- **约束**: CASCADE DELETE - 用户删除时，其反馈也会被删除
- **索引**: `idx_user_id` 支持查询用户的反馈历史

### 6. 管理员与反馈审核关系 (ct_admin_users → ct_clue_feedbacks)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_clue_feedbacks.audit_user_id` → `ct_admin_users.id`
- **业务含义**: 一个管理员可以审核多个反馈
- **约束**: SET NULL - 管理员删除时，审核记录保留但审核人置空
- **索引**: 支持查询管理员的反馈审核历史

### 7. 线索与统计关系 (ct_water_clues → ct_clue_statistics)
- **关系类型**: 一对多 (1:N)
- **外键**: `ct_clue_statistics.clue_id` → `ct_water_clues.id`
- **业务含义**: 一个线索可以有多个统计记录（按日期和类型）
- **约束**: CASCADE DELETE - 线索删除时，统计数据也会被删除
- **唯一约束**: `uk_clue_date_type` 防止同一线索在同一日期的同类型统计重复

## 数据流向分析

### 1. 线索发布流程
```
用户(ct_users) → 提交线索 → 放水线索表(ct_water_clues)
                                    ↓
管理员(ct_admin_users) → 审核线索 → 更新审核状态
                                    ↓
系统自动 → 初始化统计 → 线索统计表(ct_clue_statistics)
```

### 2. 反馈提交流程
```
用户(ct_users) → 查看线索 → 放水线索表(ct_water_clues.view_count++)
                    ↓
用户(ct_users) → 提交反馈 → 线索反馈表(ct_clue_feedbacks)
                    ↓
触发器 → 更新线索统计 → 放水线索表(ct_water_clues.comment_count++)
                    ↓
触发器 → 更新统计表 → 线索统计表(ct_clue_statistics)
```

### 3. 审核管理流程
```
管理员(ct_admin_users) → 审核线索 → 放水线索表(ct_water_clues.audit_status)
                           ↓
管理员(ct_admin_users) → 审核反馈 → 线索反馈表(ct_clue_feedbacks.audit_status)
```

## 索引策略

### 1. 主键索引
- 所有表的`id`字段自动创建主键索引
- 使用`bigint`类型支持大数据量

### 2. 外键索引
- 所有外键字段自动创建索引
- 支持关联查询的性能优化

### 3. 业务索引
- **时间索引**: `water_time`, `created_at` 支持时间排序
- **状态索引**: `audit_status`, `status` 支持状态筛选
- **评分索引**: `hot_score`, `success_rate` 支持排序查询
- **统计索引**: `view_count`, `like_count` 支持热度排序

### 4. 复合索引
- **唯一索引**: `uk_clue_date_type` 防止统计数据重复
- **查询索引**: 支持多条件组合查询

## 性能考虑

### 1. 查询优化
- 使用适当的索引支持常用查询模式
- 避免全表扫描，使用索引覆盖查询
- 对大表进行分页查询

### 2. 写入优化
- 使用触发器自动维护统计数据
- 批量操作使用事务确保一致性
- 异步处理非关键统计更新

### 3. 存储优化
- 使用适当的数据类型减少存储空间
- 对历史数据进行分区或归档
- 压缩存储大文本字段

## 扩展性设计

### 1. 水平扩展
- 支持按时间或地区进行分表
- 统计表可以按月份分区
- 支持读写分离架构

### 2. 功能扩展
- 预留扩展字段支持新功能
- JSON字段存储灵活配置
- 支持插件化的统计指标

### 3. 数据扩展
- 支持多媒体文件关联
- 支持地理位置精确定位
- 支持实时数据同步