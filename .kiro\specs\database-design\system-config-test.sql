-- =============================================
-- 系统配置和统计表结构测试SQL脚本
-- 创建时间: 2025-01-08
-- 描述: 验证系统配置管理、操作日志记录和数据统计分析功能的完整性测试
-- =============================================

-- 测试环境准备
SET FOREIGN_KEY_CHECKS = 0;

-- 清理测试数据（如果存在）
DROP TABLE IF EXISTS ct_statistics;
DROP TABLE IF EXISTS ct_operation_logs;
DROP TABLE IF EXISTS ct_system_configs;

-- 重新创建表结构（这里假设已经执行了system-config-tables.sql）

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 测试数据准备
-- =============================================

-- 1. 测试系统配置管理
SELECT '=== 测试1: 系统配置管理 ===' as test_name;

-- 验证初始配置数据
SELECT 
    config_id,
    config_key,
    config_name,
    config_value,
    config_type,
    config_group,
    is_public,
    is_active,
    version
FROM ct_system_configs 
ORDER BY config_group, sort_order;

-- 添加自定义配置测试
INSERT INTO ct_system_configs (
    config_key, config_name, config_value, config_type, config_group,
    config_description, default_value, is_public, is_editable, sort_order, created_by
) VALUES 
('test.custom.setting', '测试自定义配置', '100', 'number', 'test', '用于测试的自定义配置项', '50', 0, 1, 1, 1001),
('test.json.config', '测试JSON配置', '{"key1": "value1", "key2": 123}', 'json', 'test', 'JSON格式的配置测试', '{}', 0, 1, 2, 1001),
('test.boolean.flag', '测试布尔配置', 'true', 'boolean', 'test', '布尔类型的配置测试', 'false', 1, 1, 3, 1001);

-- 验证配置分组查询
SELECT 
    config_group,
    COUNT(*) as config_count,
    GROUP_CONCAT(config_key ORDER BY sort_order) as config_keys
FROM ct_system_configs 
WHERE is_active = 1
GROUP BY config_group
ORDER BY config_group;

-- 测试配置值更新
UPDATE ct_system_configs 
SET config_value = '200', updated_by = 1001
WHERE config_key = 'test.custom.setting';

-- 验证版本号自动更新
SELECT 
    config_key,
    config_value,
    version,
    updated_by,
    updated_at
FROM ct_system_configs 
WHERE config_key = 'test.custom.setting';

-- =============================================
-- 测试2: 存储过程功能
-- =============================================

SELECT '=== 测试2: 存储过程功能 ===' as test_name;

-- 测试获取配置值存储过程
SET @config_value = '';
CALL sp_get_config_value('point.evaluation.reward', @config_value);
SELECT '获取配置值测试' as test_type, @config_value as config_value;

-- 测试更新配置值存储过程
CALL sp_update_config_value('test.custom.setting', '300', 1002);

-- 验证配置更新结果
SELECT 
    config_key,
    config_value,
    version,
    updated_by
FROM ct_system_configs 
WHERE config_key = 'test.custom.setting';

-- =============================================
-- 测试3: 操作日志记录
-- =============================================

SELECT '=== 测试3: 操作日志记录 ===' as test_name;

-- 插入各种类型的操作日志
INSERT INTO ct_operation_logs (
    operator_id, operator_type, operator_name, module, action,
    resource_type, resource_id, operation_desc, request_method, request_url,
    request_params, response_data, operation_result, ip_address, processing_time
) VALUES
-- 用户操作日志
(1001, 'user', '测试用户1', 'evaluation', 'submit_report', 'report', 5001, '提交评测报告', 'POST', '/api/evaluation/submit',
 JSON_OBJECT('app_id', 2001, 'title', '微信评测'), JSON_OBJECT('report_id', 5001, 'status', 'success'), 'success', '*************', 1200),

-- 管理员操作日志
(1001, 'admin', '管理员1', 'system_config', 'update_config', 'config', 1, '更新系统配置', 'PUT', '/admin/config/update',
 JSON_OBJECT('config_key', 'point.evaluation.reward', 'new_value', '15'), JSON_OBJECT('success', true), 'success', '*************', 800),

-- 系统操作日志
(NULL, 'system', 'System', 'audit', 'auto_review', 'audit', 2001, '自动审核处理', NULL, NULL,
 JSON_OBJECT('audit_id', 2001, 'score', 85), JSON_OBJECT('result', 'passed'), 'success', NULL, 500),

-- API操作日志
(NULL, 'api', 'ThirdPartyAPI', 'file', 'upload_file', 'file', 3001, '第三方文件上传', 'POST', '/api/file/upload',
 JSON_OBJECT('file_name', 'test.jpg', 'file_size', 102400), JSON_OBJECT('file_id', 3001, 'url', 'https://cdn.example.com/test.jpg'), 'success', '203.0.113.1', 2000);

-- 验证操作日志查询
SELECT 
    log_id,
    operator_type,
    operator_name,
    module,
    action,
    operation_desc,
    operation_result,
    processing_time,
    created_at
FROM ct_operation_logs 
ORDER BY created_at DESC;

-- 测试操作日志存储过程
CALL sp_log_operation(1002, 'user', 'clue', 'submit_clue', 'clue', 6001, '提交放水线索', '*************', 900);

-- 验证存储过程记录的日志
SELECT 
    operator_id,
    operator_type,
    module,
    action,
    operation_desc,
    ip_address,
    processing_time
FROM ct_operation_logs 
WHERE operator_id = 1002 AND module = 'clue'
ORDER BY created_at DESC LIMIT 1;

-- =============================================
-- 测试4: 统计数据管理
-- =============================================

SELECT '=== 测试4: 统计数据管理 ===' as test_name;

-- 插入各种类型的统计数据
INSERT INTO ct_statistics (
    stat_key, stat_name, stat_type, stat_category, stat_dimension,
    stat_value, stat_count, stat_sum, stat_min, stat_max,
    time_period, stat_date, stat_data
) VALUES
-- 用户活跃度统计
('user_active_count', '日活跃用户数', 'gauge', 'user_activity', NULL, 1250, 1, 1250, 1250, 1250, 'daily', CURDATE(), 
 JSON_OBJECT('new_users', 50, 'returning_users', 1200)),

('user_active_count', '小时活跃用户数', 'gauge', 'user_activity', NULL, 180, 1, 180, 180, 180, 'hourly', CURDATE(),
 JSON_OBJECT('peak_hour', 14, 'avg_session_time', 25.5)),

-- 内容提交量统计
('content_submit_count', '评测报告提交量', 'counter', 'content_submission', 'evaluation_report', 45, 45, 45, 1, 1, 'daily', CURDATE(),
 JSON_OBJECT('approved', 40, 'rejected', 3, 'pending', 2)),

('content_submit_count', '线索提交量', 'counter', 'content_submission', 'water_clue', 28, 28, 28, 1, 1, 'daily', CURDATE(),
 JSON_OBJECT('approved', 25, 'rejected', 2, 'pending', 1)),

-- 积分发放统计
('point_distribution', '积分发放总量', 'counter', 'point_system', NULL, 1580, 73, 1580, 5, 50, 'daily', CURDATE(),
 JSON_OBJECT('evaluation_rewards', 800, 'clue_rewards', 350, 'signin_rewards', 430)),

-- APP评测数据统计
('app_evaluation_stats', 'APP评测统计', 'summary', 'app_evaluation', NULL, 4.2, 45, 189, 1, 5, 'daily', CURDATE(),
 JSON_OBJECT('avg_rating', 4.2, 'total_evaluations', 45, 'top_category', '社交通讯'));

-- 验证统计数据查询
SELECT 
    stat_key,
    stat_name,
    stat_category,
    stat_dimension,
    stat_value,
    stat_count,
    stat_avg,
    time_period,
    stat_date
FROM ct_statistics 
ORDER BY stat_category, stat_key, time_period;

-- 测试统计数据更新存储过程
CALL sp_update_statistics('user_active_count', 'user_activity', NULL, 'daily', CURDATE(), 50);

-- 验证统计数据更新结果
SELECT 
    stat_key,
    stat_category,
    stat_value,
    stat_count,
    stat_sum,
    stat_avg,
    stat_max
FROM ct_statistics 
WHERE stat_key = 'user_active_count' AND time_period = 'daily' AND stat_date = CURDATE();

-- =============================================
-- 测试5: 触发器功能验证
-- =============================================

SELECT '=== 测试5: 触发器功能验证 ===' as test_name;

-- 测试配置变更触发器
UPDATE ct_system_configs 
SET config_value = 'false', updated_by = 1002
WHERE config_key = 'test.boolean.flag';

-- 验证触发器生成的操作日志
SELECT 
    module,
    action,
    resource_type,
    resource_id,
    operation_desc,
    JSON_EXTRACT(request_params, '$.config_key') as config_key,
    JSON_EXTRACT(request_params, '$.old_value') as old_value,
    JSON_EXTRACT(request_params, '$.new_value') as new_value
FROM ct_operation_logs 
WHERE module = 'system_config' AND action = 'update_config'
ORDER BY created_at DESC LIMIT 1;

-- 验证版本号自动更新
SELECT 
    config_key,
    config_value,
    version,
    updated_by
FROM ct_system_configs 
WHERE config_key = 'test.boolean.flag';

-- 测试统计数据计算触发器
INSERT INTO ct_statistics (
    stat_key, stat_name, stat_type, stat_category, time_period, stat_date,
    stat_value, stat_count, stat_sum, stat_min, stat_max
) VALUES (
    'trigger_test', '触发器测试统计', 'counter', 'test', 'hourly', NOW(),
    100, 5, 500, 50, 150
);

-- 验证触发器自动计算的字段
SELECT 
    stat_key,
    stat_hour,
    stat_year,
    stat_avg,
    is_calculated,
    calculation_time
FROM ct_statistics 
WHERE stat_key = 'trigger_test'
ORDER BY created_at DESC LIMIT 1;

-- =============================================
-- 测试6: 复杂查询场景
-- =============================================

SELECT '=== 测试6: 复杂查询场景 ===' as test_name;

-- 6.1 配置分组统计查询
SELECT 
    '配置分组统计' as query_description;

SELECT 
    config_group,
    COUNT(*) as total_configs,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_configs,
    SUM(CASE WHEN is_public = 1 THEN 1 ELSE 0 END) as public_configs,
    MAX(version) as max_version,
    MAX(updated_at) as last_updated
FROM ct_system_configs
GROUP BY config_group
ORDER BY config_group;

-- 6.2 操作日志模块统计
SELECT 
    '操作日志模块统计' as query_description;

SELECT 
    module,
    operator_type,
    COUNT(*) as operation_count,
    AVG(processing_time) as avg_processing_time,
    SUM(CASE WHEN operation_result = 'success' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN operation_result = 'failure' THEN 1 ELSE 0 END) as failure_count,
    ROUND(SUM(CASE WHEN operation_result = 'success' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM ct_operation_logs
GROUP BY module, operator_type
ORDER BY module, operator_type;

-- 6.3 统计数据时间序列查询
SELECT 
    '统计数据时间序列' as query_description;

SELECT 
    stat_category,
    time_period,
    COUNT(*) as stat_count,
    SUM(stat_value) as total_value,
    AVG(stat_value) as avg_value,
    MIN(stat_date) as earliest_date,
    MAX(stat_date) as latest_date
FROM ct_statistics
GROUP BY stat_category, time_period
ORDER BY stat_category, time_period;

-- 6.4 用户操作行为分析
SELECT 
    '用户操作行为分析' as query_description;

SELECT 
    ol.operator_id,
    u.nickname,
    COUNT(*) as total_operations,
    COUNT(DISTINCT ol.module) as modules_used,
    AVG(ol.processing_time) as avg_processing_time,
    MAX(ol.created_at) as last_operation_time,
    GROUP_CONCAT(DISTINCT ol.module ORDER BY ol.module) as used_modules
FROM ct_operation_logs ol
LEFT JOIN ct_users u ON ol.operator_id = u.user_id
WHERE ol.operator_type = 'user' AND ol.operator_id IS NOT NULL
GROUP BY ol.operator_id, u.nickname
HAVING total_operations > 0
ORDER BY total_operations DESC;

-- =============================================
-- 测试7: 性能测试查询
-- =============================================

SELECT '=== 测试7: 性能测试查询 ===' as test_name;

-- 7.1 测试配置查询索引效果
EXPLAIN SELECT 
    config_key,
    config_value,
    config_type
FROM ct_system_configs
WHERE config_group = 'point' 
AND is_active = 1
ORDER BY sort_order;

-- 7.2 测试操作日志复合索引
EXPLAIN SELECT 
    log_id,
    action,
    operation_desc,
    created_at
FROM ct_operation_logs
WHERE operator_id = 1001
AND module = 'evaluation'
ORDER BY created_at DESC
LIMIT 10;

-- 7.3 测试统计数据时间查询
EXPLAIN SELECT 
    stat_key,
    stat_value,
    stat_date
FROM ct_statistics
WHERE stat_category = 'user_activity'
AND time_period = 'daily'
AND stat_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()
ORDER BY stat_date DESC;

-- =============================================
-- 测试8: 数据完整性验证
-- =============================================

SELECT '=== 测试8: 数据完整性验证 ===' as test_name;

-- 8.1 验证外键约束
-- 尝试插入不存在的创建人ID（应该失败）
-- INSERT INTO ct_system_configs (config_key, config_name, config_value, created_by) 
-- VALUES ('test.invalid', '无效测试', 'value', 9999);

-- 8.2 验证唯一约束
-- 尝试插入重复的配置键（应该失败）
-- INSERT INTO ct_system_configs (config_key, config_name, config_value) 
-- VALUES ('point.evaluation.reward', '重复配置', '20');

-- 8.3 验证统计数据唯一约束
-- 尝试插入重复的统计记录（应该失败）
-- INSERT INTO ct_statistics (stat_key, stat_category, time_period, stat_date, stat_value) 
-- VALUES ('user_active_count', 'user_activity', 'daily', CURDATE(), 100);

-- 8.4 验证枚举值约束
-- 尝试插入无效的配置类型（应该失败）
-- INSERT INTO ct_system_configs (config_key, config_name, config_value, config_type) 
-- VALUES ('test.invalid.type', '无效类型测试', 'value', 'invalid_type');

-- =============================================
-- 测试9: 数据分析场景
-- =============================================

SELECT '=== 测试9: 数据分析场景 ===' as test_name;

-- 9.1 系统配置使用情况分析
SELECT 
    '系统配置使用情况' as analysis_type,
    config_group,
    COUNT(*) as config_count,
    AVG(version) as avg_version,
    SUM(CASE WHEN updated_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recently_updated
FROM ct_system_configs
WHERE is_active = 1
GROUP BY config_group
ORDER BY recently_updated DESC, config_count DESC;

-- 9.2 操作频率热力图数据
SELECT 
    '操作频率分析' as analysis_type,
    module,
    action,
    COUNT(*) as frequency,
    AVG(processing_time) as avg_time,
    DATE(created_at) as operation_date
FROM ct_operation_logs
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY module, action, DATE(created_at)
ORDER BY frequency DESC, avg_time DESC;

-- 9.3 统计数据趋势分析
SELECT 
    '统计趋势分析' as analysis_type,
    stat_category,
    stat_key,
    time_period,
    AVG(stat_value) as avg_value,
    STDDEV(stat_value) as value_stddev,
    MIN(stat_value) as min_value,
    MAX(stat_value) as max_value
FROM ct_statistics
GROUP BY stat_category, stat_key, time_period
HAVING COUNT(*) > 1
ORDER BY stat_category, time_period;

-- =============================================
-- 测试结果汇总
-- =============================================

SELECT '=== 测试结果汇总 ===' as test_name;

-- 统计各表的数据量
SELECT 
    'ct_system_configs' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count
FROM ct_system_configs
UNION ALL
SELECT 
    'ct_operation_logs' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN operation_result = 'success' THEN 1 END) as success_count
FROM ct_operation_logs
UNION ALL
SELECT 
    'ct_statistics' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_calculated = 1 THEN 1 END) as calculated_count
FROM ct_statistics;

-- 验证数据完整性
SELECT 
    '数据完整性检查' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM ct_system_configs sc 
            LEFT JOIN ct_admin_users au ON sc.created_by = au.admin_id 
            WHERE sc.created_by IS NOT NULL AND au.admin_id IS NULL
        ) THEN '失败：存在无效的创建人关联'
        ELSE '通过：所有配置都有有效的创建人关联'
    END as result
UNION ALL
SELECT 
    '操作日志完整性检查' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM ct_operation_logs ol 
            WHERE ol.operator_type = 'user' AND ol.operator_id IS NOT NULL
            AND NOT EXISTS (SELECT 1 FROM ct_users u WHERE u.user_id = ol.operator_id)
        ) THEN '失败：存在无效的用户操作日志'
        ELSE '通过：所有用户操作日志都有效'
    END as result;

-- 功能验证汇总
SELECT 
    '功能验证汇总' as summary_type,
    '配置管理' as function_name,
    CASE WHEN COUNT(*) > 0 THEN '正常' ELSE '异常' END as status
FROM ct_system_configs WHERE config_group = 'test'
UNION ALL
SELECT 
    '功能验证汇总' as summary_type,
    '日志记录' as function_name,
    CASE WHEN COUNT(*) > 0 THEN '正常' ELSE '异常' END as status
FROM ct_operation_logs WHERE module IN ('evaluation', 'system_config', 'audit', 'file')
UNION ALL
SELECT 
    '功能验证汇总' as summary_type,
    '统计分析' as function_name,
    CASE WHEN COUNT(*) > 0 THEN '正常' ELSE '异常' END as status
FROM ct_statistics WHERE stat_category IN ('user_activity', 'content_submission', 'point_system');

-- 清理测试数据（可选）
-- DELETE FROM ct_statistics WHERE stat_category = 'test';
-- DELETE FROM ct_operation_logs WHERE module = 'test';
-- DELETE FROM ct_system_configs WHERE config_group = 'test';

SELECT '=== 系统配置和统计表结构测试完成 ===' as test_complete;