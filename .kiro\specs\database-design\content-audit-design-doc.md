# 内容审核系统设计文档

## 概述

内容审核系统是次推应用的核心质量控制模块，负责对用户提交的各类内容进行自动化和人工审核，确保平台内容质量和合规性。系统支持灵活的审核规则配置、多级审核流程和完整的审核日志记录。

## 业务需求分析

### 核心功能需求
1. **内容审核管理**：对评测报告、放水线索、用户资料等内容进行审核
2. **审核规则配置**：支持不同内容类型的个性化审核规则
3. **自动审核能力**：基于关键词和评分的自动审核机制
4. **人工审核流程**：支持管理员人工审核和审核结果记录
5. **审核日志追踪**：完整记录审核过程中的所有操作和状态变更

### 业务场景
- **评测报告审核**：用户提交APP评测报告后的内容质量审核
- **线索内容审核**：放水线索信息的真实性和合规性审核
- **用户资料审核**：用户注册和资料更新时的信息审核
- **图片内容审核**：用户头像、APP图标等图片内容审核
- **批量审核处理**：管理员批量处理待审核内容

## 表结构设计

### 1. 审核规则表 (ct_audit_rules)

#### 设计目标
- 提供灵活的审核规则配置体系
- 支持不同内容类型的个性化审核流程
- 实现自动审核和人工审核的协调配合

#### 核心字段
- `rule_id`: 规则主键，自增ID
- `rule_code`: 规则代码，用于程序识别
- `content_type`: 内容类型，支持多种业务内容
- `auto_audit_enabled`: 是否启用自动审核
- `auto_audit_keywords`: 自动审核关键词配置
- `audit_timeout_hours`: 审核超时时间设置
- `reward_points/penalty_points`: 审核结果对应的积分奖惩

#### 特殊设计
- **自动审核配置**：支持关键词匹配和评分阈值设置
- **超时机制**：可配置的审核超时时间
- **积分集成**：审核结果直接关联积分奖惩
- **规则版本管理**：支持规则的启用/禁用状态

### 2. 内容审核表 (ct_content_audits)

#### 设计目标
- 记录所有需要审核的内容及其状态
- 支持自动审核和人工审核的完整流程
- 提供审核结果的详细记录和追溯

#### 核心字段
- `audit_id`: 审核主键，使用BIGINT支持大量审核记录
- `content_type/content_id`: 多态关联，支持任意业务对象
- `audit_status`: 审核状态枚举（待审核、审核中、通过、拒绝、超时）
- `auto_audit_score/result`: 自动审核的评分和结果
- `auditor_id`: 人工审核员ID
- `audit_reason`: 详细的审核原因和备注
- `content_snapshot`: JSON格式的内容快照

#### 状态流转设计
1. **pending**: 初始提交状态
2. **reviewing**: 人工审核中
3. **passed**: 审核通过
4. **rejected**: 审核拒绝
5. **timeout**: 审核超时

#### 特殊设计
- **多态关联**：通过content_type和content_id关联任意业务对象
- **双重审核**：自动审核+人工审核的组合模式
- **内容快照**：保存审核时的内容状态，便于追溯
- **优先级管理**：支持不同优先级的审核处理
- **超时处理**：自动处理超时的审核任务

### 3. 审核日志表 (ct_audit_logs)

#### 设计目标
- 完整记录审核过程中的所有操作
- 支持审核流程的追溯和分析
- 提供审核效率和质量的统计数据

#### 核心字段
- `log_id`: 日志主键，自增ID
- `audit_id`: 关联的审核记录ID
- `operator_id/operator_type`: 操作人信息和类型
- `action_type`: 操作类型（提交、分配、审核、通过、拒绝等）
- `old_status/new_status`: 状态变更记录
- `action_data`: JSON格式的操作相关数据
- `processing_time`: 操作处理耗时

#### 操作类型设计
- **submit**: 内容提交审核
- **assign**: 分配审核员
- **start_review**: 开始人工审核
- **approve**: 审核通过
- **reject**: 审核拒绝
- **timeout**: 审核超时
- **reassign**: 重新分配审核员

## 审核流程设计

### 1. 自动审核流程
```
内容提交 → 规则匹配 → 关键词检测 → 评分计算 → 结果判定
    ↓
自动通过/拒绝 或 转入人工审核
```

### 2. 人工审核流程
```
待审核队列 → 审核员分配 → 内容审核 → 结果记录 → 积分处理
```

### 3. 超时处理流程
```
超时检测 → 状态更新 → 通知处理 → 重新分配或自动处理
```

## 索引设计策略

### 主要索引
1. **审核状态索引**：`idx_audit_status`, `idx_status_priority`
2. **内容关联索引**：`idx_content_type_id`, `idx_content_type`
3. **审核员索引**：`idx_auditor_status`, `idx_auditor_id`
4. **时间索引**：`idx_submitted_at`, `idx_audit_completed_at`

### 复合索引
- `idx_auditor_status`: 支持审核员工作台查询
- `idx_submitter_status`: 支持用户审核状态查询
- `idx_audit_action`: 支持审核日志查询

## 约束设计

### 外键约束
- 审核规则与管理员的关联约束
- 审核记录与用户、规则的关联约束
- 审核日志与审核记录的级联删除约束

### 唯一性约束
- 规则代码唯一性
- 内容审核记录唯一性（一个内容对象只能有一条审核记录）

### 检查约束
- 审核状态枚举值检查
- 评分范围合理性检查
- 时间逻辑一致性检查

## 触发器设计

### 状态变更触发器
- 自动记录状态变更日志
- 设置审核时间戳
- 触发相关业务逻辑

### 超时设置触发器
- 根据规则自动设置超时时间
- 支持不同内容类型的差异化超时配置

### 日志记录触发器
- 自动记录审核提交日志
- 确保操作的完整追溯

## 性能优化策略

### 查询优化
1. **审核队列查询**：使用复合索引优化待审核内容查询
2. **状态统计查询**：使用覆盖索引提升统计查询性能
3. **日志查询优化**：按时间和操作类型建立索引

### 存储优化
1. **内容快照压缩**：JSON字段使用压缩存储
2. **日志分区**：按时间对日志表进行分区
3. **历史数据归档**：定期归档历史审核记录

### 并发优化
1. **乐观锁控制**：使用版本号控制并发审核
2. **队列分片**：按内容类型分片处理审核队列
3. **异步处理**：自动审核使用异步处理提升性能

## 安全设计

### 权限控制
- 基于角色的审核权限管理
- 审核员只能处理分配给自己的任务
- 管理员可以查看所有审核记录

### 数据安全
- 敏感内容的加密存储
- 审核日志的完整性保护
- 防止审核结果的恶意篡改

### 审计安全
- 完整的操作日志记录
- 审核员行为监控
- 异常操作告警机制

## 业务规则实现

### 自动审核规则
1. **关键词匹配**：基于预设关键词库的内容检测
2. **评分机制**：多维度评分的综合判定
3. **白名单机制**：可信用户的快速通过
4. **黑名单机制**：问题用户的重点审核

### 人工审核规则
1. **工作量均衡**：审核任务的均匀分配
2. **专业化审核**：不同类型内容的专业审核员
3. **二次审核**：重要内容的多人审核机制
4. **申诉处理**：审核结果的申诉和复审流程

### 积分集成规则
1. **通过奖励**：审核通过后的积分奖励
2. **拒绝惩罚**：审核不通过的积分扣除
3. **质量评估**：基于审核结果的用户信用评级
4. **激励机制**：高质量内容的额外奖励

## 扩展性设计

### 功能扩展
- 机器学习审核模型集成
- 图像识别和文本分析
- 多语言内容审核支持
- 实时审核和批量审核

### 性能扩展
- 分布式审核处理
- 缓存层优化
- 消息队列集成
- 微服务架构支持

### 集成扩展
- 第三方审核服务集成
- 内容安全API对接
- 审核结果推送机制
- 数据分析和报表系统

## 监控和运维

### 监控指标
- 审核处理效率统计
- 审核质量评估指标
- 系统性能监控
- 异常情况告警

### 运维策略
- 审核队列监控和调度
- 审核员工作量统计
- 系统性能调优
- 数据备份和恢复

### 质量保证
- 审核结果抽检机制
- 审核员培训和考核
- 审核标准持续优化
- 用户反馈处理机制