-- =============================================
-- 次推应用 - 用户管理相关表结构设计
-- 创建时间: 2025-01-08
-- 描述: 包含用户基础信息、登录记录、用户关系和管理员用户表
-- =============================================

-- 设置字符集和存储引擎
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 用户基础信息表 (ct_users)
-- 用途: 存储用户基本信息和账户状态
-- =============================================
DROP TABLE IF EXISTS `ct_users`;
CREATE TABLE `ct_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `phone` varchar(20) NOT NULL COMMENT '手机号，用于登录',
  `password` varchar(255) DEFAULT NULL COMMENT '登录密码，MD5加密',
  `nick_name` varchar(50) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '用户头像URL',
  `real_name` varchar(20) DEFAULT NULL COMMENT '真实姓名，用于实名认证',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号，用于实名认证',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱地址',
  `wechat_openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `wechat_unionid` varchar(100) DEFAULT NULL COMMENT '微信UnionID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户状态：0-禁用，1-正常，2-待审核',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否实名认证：0-未认证，1-已认证',
  `total_points` int(11) NOT NULL DEFAULT 0 COMMENT '用户总积分',
  `available_points` int(11) NOT NULL DEFAULT 0 COMMENT '可用积分',
  `invite_code` varchar(20) DEFAULT NULL COMMENT '用户邀请码',
  `referrer_id` bigint(20) unsigned DEFAULT NULL COMMENT '推荐人用户ID',
  `referrer_level` tinyint(2) NOT NULL DEFAULT 0 COMMENT '推荐层级：0-直接用户，1-一级推荐，2-二级推荐',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数统计',
  `register_ip` varchar(50) DEFAULT NULL COMMENT '注册IP地址',
  `register_device` varchar(200) DEFAULT NULL COMMENT '注册设备信息',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_verified` (`is_verified`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_wechat_openid` (`wechat_openid`),
  CONSTRAINT `fk_users_referrer` FOREIGN KEY (`referrer_id`) REFERENCES `ct_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- =============================================
-- 2. 用户登录记录表 (ct_user_logins)
-- 用途: 记录用户登录历史和设备信息
-- =============================================
DROP TABLE IF EXISTS `ct_user_logins`;
CREATE TABLE `ct_user_logins` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID，主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `login_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '登录类型：1-密码登录，2-验证码登录，3-微信登录',
  `login_ip` varchar(50) NOT NULL COMMENT '登录IP地址',
  `login_location` varchar(100) DEFAULT NULL COMMENT '登录地理位置',
  `device_type` varchar(20) DEFAULT NULL COMMENT '设备类型：ios/android/web',
  `device_model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备唯一标识',
  `os_version` varchar(50) DEFAULT NULL COMMENT '操作系统版本',
  `app_version` varchar(20) DEFAULT NULL COMMENT 'APP版本号',
  `user_agent` text DEFAULT NULL COMMENT '用户代理信息',
  `network_type` varchar(20) DEFAULT NULL COMMENT '网络类型：wifi/4g/5g',
  `screen_resolution` varchar(20) DEFAULT NULL COMMENT '屏幕分辨率',
  `login_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '登录状态：0-失败，1-成功',
  `failure_reason` varchar(200) DEFAULT NULL COMMENT '登录失败原因',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `logout_time` datetime DEFAULT NULL COMMENT '退出登录时间',
  `session_duration` int(11) DEFAULT NULL COMMENT '会话持续时间（秒）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_ip` (`login_ip`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_login_status` (`login_status`),
  CONSTRAINT `fk_user_logins_user_id` FOREIGN KEY (`user_id`) REFERENCES `ct_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录记录表';

-- =============================================
-- 3. 用户关系表 (ct_user_relations)
-- 用途: 管理用户推荐关系和层级结构
-- =============================================
DROP TABLE IF EXISTS `ct_user_relations`;
CREATE TABLE `ct_user_relations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关系ID，主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `referrer_id` bigint(20) unsigned NOT NULL COMMENT '推荐人用户ID',
  `relation_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '关系类型：1-直接推荐，2-间接推荐',
  `relation_level` tinyint(2) NOT NULL DEFAULT 1 COMMENT '推荐层级：1-一级，2-二级，3-三级等',
  `bind_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `bind_ip` varchar(50) DEFAULT NULL COMMENT '绑定IP地址',
  `bind_device` varchar(200) DEFAULT NULL COMMENT '绑定设备信息',
  `invite_code_used` varchar(20) DEFAULT NULL COMMENT '使用的邀请码',
  `reward_points` int(11) NOT NULL DEFAULT 0 COMMENT '推荐奖励积分',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '关系是否有效：0-无效，1-有效',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_referrer` (`user_id`, `referrer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_relation_level` (`relation_level`),
  KEY `idx_bind_time` (`bind_time`),
  KEY `idx_invite_code` (`invite_code_used`),
  CONSTRAINT `fk_user_relations_user_id` FOREIGN KEY (`user_id`) REFERENCES `ct_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_relations_referrer_id` FOREIGN KEY (`referrer_id`) REFERENCES `ct_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关系表';

-- =============================================
-- 4. 管理员用户表 (ct_admin_users)
-- 用途: 管理系统管理员账户和权限
-- =============================================
DROP TABLE IF EXISTS `ct_admin_users`;
CREATE TABLE `ct_admin_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员ID，主键',
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `password` varchar(255) NOT NULL COMMENT '登录密码，MD5加密',
  `real_name` varchar(50) NOT NULL COMMENT '管理员真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `role_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '角色类型：1-超级管理员，2-普通管理员，3-审核员',
  `permissions` text DEFAULT NULL COMMENT '权限列表，JSON格式存储',
  `department` varchar(50) DEFAULT NULL COMMENT '所属部门',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-锁定',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `password_updated_at` datetime DEFAULT NULL COMMENT '密码最后更新时间',
  `is_super_admin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否超级管理员：0-否，1-是',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_role_type` (`role_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_admin_users_created_by` FOREIGN KEY (`created_by`) REFERENCES `ct_admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入默认超级管理员账户
INSERT INTO `ct_admin_users` (`username`, `password`, `real_name`, `role_type`, `is_super_admin`, `status`, `created_at`) 
VALUES ('admin', MD5('admin123'), '系统管理员', 1, 1, 1, NOW());

-- =============================================
-- 索引优化说明
-- =============================================
/*
1. ct_users表索引设计：
   - 主键索引：id（自动创建）
   - 唯一索引：phone（登录查询）、invite_code（邀请码查询）
   - 普通索引：referrer_id（推荐关系查询）、status（状态筛选）、is_verified（认证状态）
   - 时间索引：created_at（注册时间排序）
   - 微信索引：wechat_openid（微信登录）

2. ct_user_logins表索引设计：
   - 主键索引：id（自动创建）
   - 外键索引：user_id（用户登录记录查询）
   - 普通索引：login_ip（IP查询）、device_id（设备查询）、login_status（状态筛选）
   - 时间索引：created_at（登录时间排序）

3. ct_user_relations表索引设计：
   - 主键索引：id（自动创建）
   - 唯一索引：user_id + referrer_id（防止重复关系）
   - 外键索引：user_id、referrer_id（关系查询）
   - 普通索引：relation_level（层级查询）、invite_code_used（邀请码查询）
   - 时间索引：bind_time（绑定时间排序）

4. ct_admin_users表索引设计：
   - 主键索引：id（自动创建）
   - 唯一索引：username（登录查询）
   - 普通索引：email、phone（联系方式查询）、role_type（角色筛选）、status（状态筛选）
   - 外键索引：created_by（创建人查询）
   - 时间索引：created_at（创建时间排序）
*/

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;