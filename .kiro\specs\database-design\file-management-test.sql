-- =============================================
-- 文件管理系统测试SQL脚本
-- 创建时间: 2025-01-08
-- 描述: 验证文件管理系统表结构和功能的完整性测试
-- =============================================

-- 测试环境准备
SET FOREIGN_KEY_CHECKS = 0;

-- 清理测试数据（如果存在）
DROP TABLE IF EXISTS ct_file_relations;
DROP TABLE IF EXISTS ct_files;
DROP TABLE IF EXISTS ct_file_categories;

-- 重新创建表结构（这里假设已经执行了file-management-tables.sql）

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 测试数据准备
-- =============================================

-- 1. 测试文件分类功能
SELECT '=== 测试1: 文件分类管理 ===' as test_name;

-- 验证初始分类数据
SELECT 
    category_id,
    category_name,
    category_code,
    parent_id,
    max_file_size,
    allowed_extensions
FROM ct_file_categories 
ORDER BY sort_order;

-- 添加子分类测试
INSERT INTO ct_file_categories (category_name, category_code, parent_id, sort_order, max_file_size, allowed_extensions, description) VALUES
('评测收益截图', 'evaluation_income', 2, 21, 5242880, 'jpg,jpeg,png,webp', '评测收益相关截图'),
('评测过程截图', 'evaluation_process', 2, 22, 5242880, 'jpg,jpeg,png,webp', '评测过程相关截图');

-- 验证层级分类查询
SELECT 
    c1.category_name as parent_category,
    c2.category_name as child_category,
    c2.category_code,
    c2.max_file_size
FROM ct_file_categories c1
RIGHT JOIN ct_file_categories c2 ON c1.category_id = c2.parent_id
ORDER BY c1.sort_order, c2.sort_order;

-- =============================================
-- 测试2: 文件信息管理
-- =============================================

SELECT '=== 测试2: 文件信息管理 ===' as test_name;

-- 模拟用户数据（假设用户表已存在）
INSERT IGNORE INTO ct_users (user_id, phone, nickname, avatar_url, created_at) VALUES
(1001, '13800138001', '测试用户1', 'https://example.com/avatar1.jpg', NOW()),
(1002, '13800138002', '测试用户2', 'https://example.com/avatar2.jpg', NOW()),
(1003, '13800138003', '测试用户3', 'https://example.com/avatar3.jpg', NOW());

-- 插入测试文件数据
INSERT INTO ct_files (
    category_id, uploader_id, original_name, stored_name, file_path, file_url,
    file_size, file_type, file_extension, file_hash, width, height,
    upload_ip, upload_device, storage_type, is_public
) VALUES
-- APP图标文件
(1, 1001, 'wechat_logo.png', 'uuid_001.png', '/uploads/app_logos/uuid_001.png', 'https://cdn.example.com/uuid_001.png', 
 45678, 'image/png', 'png', 'md5hash001', 512, 512, '*************', 'iPhone 13', 'oss', 1),

-- 评测截图文件
(2, 1001, '评测收益截图.jpg', 'uuid_002.jpg', '/uploads/screenshots/uuid_002.jpg', 'https://cdn.example.com/uuid_002.jpg',
 234567, 'image/jpeg', 'jpg', 'md5hash002', 1080, 1920, '*************', 'iPhone 13', 'oss', 1),

(2, 1002, '任务完成截图.png', 'uuid_003.png', '/uploads/screenshots/uuid_003.png', 'https://cdn.example.com/uuid_003.png',
 345678, 'image/png', 'png', 'md5hash003', 1080, 2340, '*************', 'Android', 'local', 1),

-- 线索截图文件
(3, 1002, '放水线索截图.jpg', 'uuid_004.jpg', '/uploads/clues/uuid_004.jpg', 'https://cdn.example.com/uuid_004.jpg',
 456789, 'image/jpeg', 'jpg', 'md5hash004', 750, 1334, '*************', 'Android', 'oss', 1),

-- 用户头像文件
(4, 1003, '我的头像.jpg', 'uuid_005.jpg', '/uploads/avatars/uuid_005.jpg', 'https://cdn.example.com/uuid_005.jpg',
 123456, 'image/jpeg', 'jpg', 'md5hash005', 200, 200, '*************', 'iPad', 'local', 1);

-- 验证文件信息查询
SELECT 
    f.file_id,
    f.original_name,
    f.file_size,
    f.file_type,
    c.category_name,
    u.nickname as uploader,
    f.storage_type,
    f.created_at
FROM ct_files f
JOIN ct_file_categories c ON f.category_id = c.category_id
JOIN ct_users u ON f.uploader_id = u.user_id
ORDER BY f.created_at DESC;

-- =============================================
-- 测试3: 文件关联管理
-- =============================================

SELECT '=== 测试3: 文件关联管理 ===' as test_name;

-- 模拟业务对象数据
INSERT IGNORE INTO ct_apps (app_id, app_name, logo_url, created_at) VALUES
(2001, '微信', 'https://cdn.example.com/uuid_001.png', NOW()),
(2002, '支付宝', 'https://cdn.example.com/app2_logo.png', NOW());

INSERT IGNORE INTO ct_evaluation_reports (report_id, app_id, user_id, created_at) VALUES
(3001, 2001, 1001, NOW()),
(3002, 2002, 1002, NOW());

INSERT IGNORE INTO ct_water_clues (clue_id, app_id, user_id, created_at) VALUES
(4001, 2001, 1002, NOW()),
(4002, 2002, 1003, NOW());

-- 建立文件关联关系
INSERT INTO ct_file_relations (file_id, business_type, business_id, relation_type, sort_order, is_primary, description) VALUES
-- APP图标关联
(1, 'app_logo', 2001, 'cover', 1, 1, '微信应用图标'),

-- 评测截图关联
(2, 'evaluation_screenshot', 3001, 'attachment', 1, 1, '微信评测收益截图'),
(3, 'evaluation_screenshot', 3002, 'attachment', 1, 1, '支付宝评测截图'),

-- 线索截图关联
(4, 'clue_screenshot', 4001, 'attachment', 1, 1, '微信放水线索截图'),

-- 用户头像关联
(5, 'user_avatar', 1003, 'cover', 1, 1, '用户头像');

-- 验证文件关联查询
SELECT 
    fr.relation_id,
    f.original_name,
    fr.business_type,
    fr.business_id,
    fr.relation_type,
    fr.is_primary,
    c.category_name
FROM ct_file_relations fr
JOIN ct_files f ON fr.file_id = f.file_id
JOIN ct_file_categories c ON f.category_id = c.category_id
ORDER BY fr.business_type, fr.business_id, fr.sort_order;

-- =============================================
-- 测试4: 复杂查询场景
-- =============================================

SELECT '=== 测试4: 复杂查询场景 ===' as test_name;

-- 4.1 获取特定APP的所有关联文件
SELECT 
    '获取微信APP的所有关联文件' as query_description;

SELECT 
    a.app_name,
    f.original_name,
    f.file_size,
    c.category_name,
    fr.relation_type,
    u.nickname as uploader
FROM ct_apps a
JOIN ct_file_relations fr ON fr.business_type = 'app_logo' AND fr.business_id = a.app_id
JOIN ct_files f ON fr.file_id = f.file_id
JOIN ct_file_categories c ON f.category_id = c.category_id
JOIN ct_users u ON f.uploader_id = u.user_id
WHERE a.app_id = 2001;

-- 4.2 获取用户上传的所有文件统计
SELECT 
    '用户文件上传统计' as query_description;

SELECT 
    u.nickname,
    c.category_name,
    COUNT(*) as file_count,
    SUM(f.file_size) as total_size,
    AVG(f.file_size) as avg_size
FROM ct_users u
JOIN ct_files f ON u.user_id = f.uploader_id
JOIN ct_file_categories c ON f.category_id = c.category_id
WHERE f.is_deleted = 0
GROUP BY u.user_id, c.category_id
ORDER BY u.nickname, c.sort_order;

-- 4.3 获取评测报告的所有截图
SELECT 
    '评测报告截图查询' as query_description;

SELECT 
    er.report_id,
    a.app_name,
    u.nickname as reporter,
    f.original_name,
    f.file_url,
    fr.sort_order
FROM ct_evaluation_reports er
JOIN ct_apps a ON er.app_id = a.app_id
JOIN ct_users u ON er.user_id = u.user_id
JOIN ct_file_relations fr ON fr.business_type = 'evaluation_screenshot' AND fr.business_id = er.report_id
JOIN ct_files f ON fr.file_id = f.file_id
WHERE er.report_id = 3001
ORDER BY fr.sort_order;

-- =============================================
-- 测试5: 约束和触发器测试
-- =============================================

SELECT '=== 测试5: 约束和触发器测试 ===' as test_name;

-- 5.1 测试文件软删除触发器
UPDATE ct_files SET is_deleted = 1 WHERE file_id = 5;

-- 验证软删除触发器是否正确设置deleted_at
SELECT 
    file_id,
    original_name,
    is_deleted,
    deleted_at,
    '软删除触发器测试' as test_type
FROM ct_files 
WHERE file_id = 5;

-- 验证关联清理触发器是否工作
SELECT 
    COUNT(*) as relation_count,
    '关联清理触发器测试' as test_type
FROM ct_file_relations 
WHERE file_id = 5;

-- 5.2 恢复软删除测试
UPDATE ct_files SET is_deleted = 0 WHERE file_id = 5;

SELECT 
    file_id,
    original_name,
    is_deleted,
    deleted_at,
    '软删除恢复测试' as test_type
FROM ct_files 
WHERE file_id = 5;

-- =============================================
-- 测试6: 性能测试查询
-- =============================================

SELECT '=== 测试6: 性能测试查询 ===' as test_name;

-- 6.1 测试索引效果 - 按分类查询文件
EXPLAIN SELECT 
    f.file_id,
    f.original_name,
    f.file_size,
    c.category_name
FROM ct_files f
JOIN ct_file_categories c ON f.category_id = c.category_id
WHERE c.category_code = 'evaluation_screenshot'
AND f.is_deleted = 0
ORDER BY f.created_at DESC
LIMIT 10;

-- 6.2 测试复合索引 - 用户分类文件查询
EXPLAIN SELECT 
    f.file_id,
    f.original_name,
    f.file_size
FROM ct_files f
WHERE f.uploader_id = 1001
AND f.category_id = 2
AND f.is_deleted = 0
ORDER BY f.created_at DESC;

-- 6.3 测试业务关联查询性能
EXPLAIN SELECT 
    f.original_name,
    f.file_url,
    fr.relation_type
FROM ct_file_relations fr
JOIN ct_files f ON fr.file_id = f.file_id
WHERE fr.business_type = 'evaluation_screenshot'
AND fr.business_id = 3001
ORDER BY fr.sort_order;

-- =============================================
-- 测试7: 数据完整性验证
-- =============================================

SELECT '=== 测试7: 数据完整性验证 ===' as test_name;

-- 7.1 验证外键约束
-- 尝试插入不存在的分类ID（应该失败）
-- INSERT INTO ct_files (category_id, uploader_id, original_name, stored_name, file_path, file_url, file_size, file_type, file_extension, file_hash) 
-- VALUES (999, 1001, 'test.jpg', 'test.jpg', '/test.jpg', '/test.jpg', 1000, 'image/jpeg', 'jpg', 'testhash');

-- 7.2 验证唯一约束
-- 尝试插入重复的分类代码（应该失败）
-- INSERT INTO ct_file_categories (category_name, category_code) VALUES ('重复分类', 'app_logo');

-- 7.3 验证文件关联唯一约束
-- 尝试插入重复的文件业务关联（应该失败）
-- INSERT INTO ct_file_relations (file_id, business_type, business_id, relation_type) VALUES (1, 'app_logo', 2001, 'cover');

-- =============================================
-- 测试结果汇总
-- =============================================

SELECT '=== 测试结果汇总 ===' as test_name;

-- 统计各表的数据量
SELECT 
    'ct_file_categories' as table_name,
    COUNT(*) as record_count
FROM ct_file_categories
UNION ALL
SELECT 
    'ct_files' as table_name,
    COUNT(*) as record_count
FROM ct_files
UNION ALL
SELECT 
    'ct_file_relations' as table_name,
    COUNT(*) as record_count
FROM ct_file_relations;

-- 验证数据完整性
SELECT 
    '数据完整性检查' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM ct_files f 
            LEFT JOIN ct_file_categories c ON f.category_id = c.category_id 
            WHERE c.category_id IS NULL
        ) THEN '失败：存在无效的分类关联'
        ELSE '通过：所有文件都有有效的分类关联'
    END as result
UNION ALL
SELECT 
    '文件关联完整性检查' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM ct_file_relations fr 
            LEFT JOIN ct_files f ON fr.file_id = f.file_id 
            WHERE f.file_id IS NULL
        ) THEN '失败：存在无效的文件关联'
        ELSE '通过：所有关联都指向有效文件'
    END as result;

-- 清理测试数据（可选）
-- DELETE FROM ct_file_relations WHERE relation_id > 0;
-- DELETE FROM ct_files WHERE file_id > 0;
-- DELETE FROM ct_file_categories WHERE category_id > 7;

SELECT '=== 文件管理系统测试完成 ===' as test_complete;