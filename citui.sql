/*
 Navicat Premium Dump SQL

 Source Server         : 本地数据库
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : citui

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 11/08/2025 22:39:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zc_ad_log
-- ----------------------------
DROP TABLE IF EXISTS `zc_ad_log`;
CREATE TABLE `zc_ad_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `ad_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户看广告数据ID',
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '看广告的用户',
  `user_pid` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '一级推广用户',
  `user_pid_2` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '二级推广用户',
  `inner_account` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '看广告的用户是否是内部账号',
  `fk` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否触发风控',
  `is_cheat` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否是小号看的广告',
  `reward_user` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '允许用户奖励',
  `reward_admin` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '允许代理奖励',
  `max_money_user` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '触发用户最高奖励',
  `max_money_agent` tinyint(1) NULL DEFAULT NULL COMMENT '触发代理最高奖励',
  `ad_log` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '看广告奖励详细日志',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ad_id`(`ad_id`, `user_id`, `fk`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 49658 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_ad_reward_cat
-- ----------------------------
DROP TABLE IF EXISTS `zc_ad_reward_cat`;
CREATE TABLE `zc_ad_reward_cat`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分销等级名称',
  `title_sub` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '副标题',
  `price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `level_1` smallint(6) NULL DEFAULT 0 COMMENT '一级推荐奖励百分比',
  `level_2` smallint(6) NULL DEFAULT 0 COMMENT '二级推荐奖励百分比',
  `order` smallint(6) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order`(`order`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户广告激励等级和小程序保持一致' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_ad_reward_gdt
-- ----------------------------
DROP TABLE IF EXISTS `zc_ad_reward_gdt`;
CREATE TABLE `zc_ad_reward_gdt`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `trans_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '交易ID',
  `reward_amount` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '奖励数量',
  `reward_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '奖励名称',
  `pid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '广告位ID',
  `app_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'appid',
  `sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签名',
  `extra` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户自定义参数字符串base64编码 允许为空',
  `extra_decode` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '如果extra有值 解码后存储',
  `receive_over` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否收到该trans_id广告完成可以奖励的通知',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '处理状态',
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证结果',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `create_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `trans_id`(`trans_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_ad_reward_gromore
-- ----------------------------
DROP TABLE IF EXISTS `zc_ad_reward_gromore`;
CREATE TABLE `zc_ad_reward_gromore`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `trans_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '交易ID',
  `reward_amount` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '奖励数量',
  `reward_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '奖励名称',
  `prime_rit` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '广告位ID',
  `mediation_rit` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '代码位ID',
  `sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签名',
  `extra` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户自定义参数字符串base64编码 允许为空',
  `extra_decode` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '如果extra有值 解码后存储',
  `receive_over` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否收到该trans_id广告完成可以奖励的通知',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '处理状态',
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证结果',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `create_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `trans_id`(`trans_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_ad_reward_ks
-- ----------------------------
DROP TABLE IF EXISTS `zc_ad_reward_ks`;
CREATE TABLE `zc_ad_reward_ks`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `trans_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '交易ID',
  `reward_amount` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '奖励数量',
  `reward_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '奖励名称',
  `pid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '广告位ID',
  `app_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'appid',
  `sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签名',
  `extra` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户自定义参数字符串base64编码 允许为空',
  `extra_decode` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '如果extra有值 解码后存储',
  `receive_over` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否收到该trans_id广告完成可以奖励的通知',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '处理状态',
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证结果',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `create_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `trans_id`(`trans_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_ad_reward_relation
-- ----------------------------
DROP TABLE IF EXISTS `zc_ad_reward_relation`;
CREATE TABLE `zc_ad_reward_relation`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `ad_id` int(11) UNSIGNED NULL DEFAULT 0,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'gromore',
  `reward_id` int(11) UNSIGNED NULL DEFAULT 0,
  `trans_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `create_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ad_id`(`ad_id`, `type`) USING BTREE,
  INDEX `trans_id`(`trans_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_admin_user
-- ----------------------------
DROP TABLE IF EXISTS `zc_admin_user`;
CREATE TABLE `zc_admin_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `status` int(10) NULL DEFAULT 1 COMMENT '管理员状态 0 禁用 1 正常\'',
  `type` tinyint(1) NOT NULL DEFAULT 2 COMMENT '类型2：代理',
  `share_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享二维码',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_advertisement
-- ----------------------------
DROP TABLE IF EXISTS `zc_advertisement`;
CREATE TABLE `zc_advertisement`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户id',
  `advertiser_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '广告商',
  `income_user` decimal(10, 3) NULL DEFAULT 0.000 COMMENT '用户收益(元)',
  `pid_1` int(11) UNSIGNED NULL DEFAULT 0,
  `income_pid_1` decimal(10, 3) NULL DEFAULT 0.000,
  `pid_2` int(11) UNSIGNED NULL DEFAULT 0,
  `income_pid_2` decimal(10, 3) NULL DEFAULT 0.000,
  `pid` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '代理人id',
  `income_pid` decimal(10, 3) NULL DEFAULT 0.000 COMMENT '代理人获得钱',
  `income` decimal(10, 3) NULL DEFAULT 0.000 COMMENT '收益(元)',
  `profit` decimal(10, 3) NULL DEFAULT 0.000 COMMENT '利润',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '',
  `m_type` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '机型 1安卓  2 ios  3平板 默认1',
  `m_version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '操作系统版本',
  `m_brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '品牌',
  `m_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '型号',
  `m_device_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '设备编码',
  `m_network` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '0未知 1WIFI 2流量  3VPN',
  `m_netserver` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '0未知 1中国移动 2中国联通 3中国电信 4中国广电',
  `m_simulator` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '是否模拟器 0 否  1是',
  `can_time` date NULL DEFAULT NULL COMMENT '看广告时间',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`, `ip`) USING BTREE,
  INDEX `m_device_id`(`m_device_id`) USING BTREE,
  INDEX `can_time`(`can_time`) USING BTREE,
  INDEX `income_user`(`income_user`) USING BTREE,
  INDEX `user_id_2`(`user_id`, `can_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '看广告记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_advertiser
-- ----------------------------
DROP TABLE IF EXISTS `zc_advertiser`;
CREATE TABLE `zc_advertiser`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告商名称',
  `as` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简称',
  `odds` int(10) NULL DEFAULT 0 COMMENT '播放概率',
  `state` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1：开启0：关闭',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_game
-- ----------------------------
DROP TABLE IF EXISTS `zc_game`;
CREATE TABLE `zc_game`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '游戏名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图标URL地址',
  `invite_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '默认注册码',
  `package` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '包名',
  `download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '下载地址',
  `reg_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '注册地址',
  `tap_cookie` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'TAP',
  `tap_app_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'TAP',
  `is_hot` tinyint(1) UNSIGNED NULL DEFAULT 0,
  `is_recomment` tinyint(1) UNSIGNED NULL DEFAULT 0,
  `status` tinyint(1) UNSIGNED NULL DEFAULT 1,
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_migrations
-- ----------------------------
DROP TABLE IF EXISTS `zc_migrations`;
CREATE TABLE `zc_migrations`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `migration` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_personal_access_tokens
-- ----------------------------
DROP TABLE IF EXISTS `zc_personal_access_tokens`;
CREATE TABLE `zc_personal_access_tokens`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `zc_personal_access_tokens_token_unique`(`token`) USING BTREE,
  INDEX `zc_personal_access_tokens_tokenable_type_tokenable_id_index`(`tokenable_type`, `tokenable_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_platform_config
-- ----------------------------
DROP TABLE IF EXISTS `zc_platform_config`;
CREATE TABLE `zc_platform_config`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `config_type` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '1广告联盟COOKIE配置',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '平台名称',
  `alias` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '别名',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'csj ylh ks',
  `site_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '应用ID',
  `code_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '广告位ID',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值',
  `info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '获取的数据  JSON格式',
  `info_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '获取数据的时间',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '状态',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `config_type`(`config_type`, `alias`, `platform`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_red_packet
-- ----------------------------
DROP TABLE IF EXISTS `zc_red_packet`;
CREATE TABLE `zc_red_packet`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NULL DEFAULT NULL COMMENT '用户代理人',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `from_user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '代理奖励时 该奖励来着用户ID',
  `ad_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '系统广告记录ID',
  `money` decimal(7, 3) NULL DEFAULT NULL COMMENT '红包金钱',
  `state` tinyint(3) NULL DEFAULT NULL COMMENT '状态： 添加：1，减少：2',
  `type` tinyint(3) NULL DEFAULT NULL COMMENT '类型 1：广告红包 2:代理奖励',
  `identity` tinyint(3) NULL DEFAULT NULL COMMENT '身份 1：用户 2:代理',
  `first_money` decimal(7, 3) NULL DEFAULT NULL COMMENT '初始资金',
  `invite_level` smallint(3) UNSIGNED NULL DEFAULT 0 COMMENT '关联用户推荐的等级 1级 还是2级推荐奖励  默认 0',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '红包记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_share
-- ----------------------------
DROP TABLE IF EXISTS `zc_share`;
CREATE TABLE `zc_share`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `share_time` date NULL DEFAULT NULL COMMENT '观看时间',
  `is_user` tinyint(1) NULL DEFAULT 0 COMMENT '是否使用 1：是 0：否',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '分享记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_species
-- ----------------------------
DROP TABLE IF EXISTS `zc_species`;
CREATE TABLE `zc_species`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `shop_id` int(11) NULL DEFAULT NULL COMMENT '商品id',
  `species` decimal(7, 2) NULL DEFAULT NULL COMMENT '金币',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '状态： 添加：1，减少：2',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '类型 ；1：购买商品 2：签到 3:红包兑换金币 4：金币兑换红包  5：看广告获得金币 6：邀请好友获得金币 7:开宝箱',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '金币记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_admin
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_admin`;
CREATE TABLE `zc_system_admin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员id',
  `role_id` int(11) NULL DEFAULT NULL COMMENT '角色id',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '管理员账号',
  `admin_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '管理员姓名',
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '管理员密码',
  `head_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '管理员头像',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` int(1) NOT NULL DEFAULT 1 COMMENT '管理员状态 0 禁用 1 正常',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',
  `login_time` int(11) NULL DEFAULT NULL COMMENT '登录时间',
  `sex` tinyint(2) NULL DEFAULT 0 COMMENT '性别  1 男 2 女',
  `birth` datetime NULL DEFAULT NULL COMMENT '出生日期',
  `real_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户姓名',
  `mobile` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `cid` int(11) NULL DEFAULT 0 COMMENT '商户id,0为总账号',
  `type` tinyint(1) NULL DEFAULT 1 COMMENT '类型1：管理员',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '后台管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_config
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_config`;
CREATE TABLE `zc_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类',
  `name` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '配置项名称',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '属性名(key)',
  `value` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '属性值',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '说明',
  `sort` int(4) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `required` int(1) NOT NULL DEFAULT 0 COMMENT '是否必填 0 否 1 必填',
  `text_type` int(1) NOT NULL DEFAULT 1 COMMENT '配置项类型 1 文本框 2 图片 3 单选 4 多选 5：富文本',
  `select_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '选项数据',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否启用',
  `data_type` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '数据类型 新增客服的使用的 区分 微信客服1 QQ 客服2 其它是3',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '后台配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_log
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_log`;
CREATE TABLE `zc_system_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `add_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 73647 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_menu
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_menu`;
CREATE TABLE `zc_system_menu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单权限ID',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级菜单',
  `web_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '前端路由地址',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由英文名字(路由标识)',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '路由中文名称',
  `api_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '后台接口地址',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `type` int(1) NOT NULL DEFAULT 1 COMMENT '权限分类 1 菜单 2 权限',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重定位路由(如果是菜单的话可以指定默认打开的页面)',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型为权限是此字段无需填写(此字段与该页面权限关联,如删除则为 [TABLE_NAME_DELETE],并与配置表中的表明保持一致)',
  `status` int(1) NOT NULL DEFAULT 1 COMMENT '状态 1显示 0隐藏',
  `belong` tinyint(1) NULL DEFAULT 1 COMMENT '类型：1：管理员 2：代理',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '许可标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限标' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_role
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_role`;
CREATE TABLE `zc_system_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `auths` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色权限',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色说明',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',
  `cid` int(11) NULL DEFAULT 0 COMMENT '商会id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_table
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_table`;
CREATE TABLE `zc_system_table`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '表格配置项ID',
  `table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称',
  `field` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表字段',
  `field_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段中文名称',
  `column_width` int(11) NULL DEFAULT 0 COMMENT '行内占比宽度',
  `required` tinyint(4) NULL DEFAULT NULL COMMENT '是否必填',
  `table_show` tinyint(1) NULL DEFAULT 1 COMMENT '表格中是否显示',
  `form_show` tinyint(1) NULL DEFAULT 1 COMMENT '表单中是否显示',
  `search_show` tinyint(1) NULL DEFAULT 0 COMMENT '搜索框中是否显示',
  `detail_show` int(1) NULL DEFAULT 0 COMMENT '详情显示',
  `text_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'string' COMMENT '字段类型:\r\n文本:string,\r\n数字:number,\r\n图片:singleGraph,\r\n多图:multiGraph,\r\n单选:radio,\r\n多选:select,\r\n布尔:boolean,\r\n富文本:richText,\r\n时间:time,\r\n日期:date,\r\n日期时间: datetime',
  `select_values` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '选项值',
  `default_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认值',
  `sort` int(3) NULL DEFAULT 0 COMMENT '字段显示排序',
  `field_reminder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段备注或者提示',
  `file_accept` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件类型',
  `file_size` int(11) NULL DEFAULT 2 COMMENT '文件大小',
  `dictionary_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典api(填写此值则选项值字段无效)',
  `dictionary_set` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典配置如:{\"label\":\"name\",\"value\":\"id\"}',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表格配置项表,用于自动生成前端表格,表单,搜索等' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_system_uploadfile
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_uploadfile`;
CREATE TABLE `zc_system_uploadfile`  (
  `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `upload_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `original_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件原名',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '物理路径',
  `image_width` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '宽度',
  `image_height` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '高度',
  `image_type` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片类型',
  `image_frames` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图片帧数',
  `mime_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'mime类型',
  `file_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小',
  `file_ext` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sha1` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '文件 sha1编码',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `upload_type`(`upload_type`) USING BTREE,
  INDEX `original_name`(`original_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '上传文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_user
-- ----------------------------
DROP TABLE IF EXISTS `zc_user`;
CREATE TABLE `zc_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NULL DEFAULT 0 COMMENT '直推人ID',
  `pid_2` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '次推人ID',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '微信openid',
  `pwd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '密码',
  `withdrawal_money` decimal(7, 2) NULL DEFAULT NULL COMMENT '提现金额',
  `level` int(10) NULL DEFAULT NULL COMMENT '关卡',
  `invite_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '注册码',
  `nick_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '昵称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '头像',
  `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '手机号',
  `identity` tinyint(3) NULL DEFAULT 1 COMMENT '身份 1：用户 2：代理人',
  `commission` int(10) NULL DEFAULT 0 COMMENT '用户分佣比例%【调整成自己看的分佣比例】',
  `admin_commission` int(10) NULL DEFAULT 0 COMMENT '代理人分佣比例',
  `admin_id` int(10) NULL DEFAULT NULL COMMENT '绑定的代理账号',
  `reward_user` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否允许用户奖励[该用户自看和推广时是否有奖励]',
  `reward_admin` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '是否允许代理奖励[意思是当前账号观看是否给上级奖励]',
  `inner_account` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否是内部账号',
  `is_cheat` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '该账号是否是小号 仅按设备判断',
  `user_commission` tinyint(3) NULL DEFAULT 0 COMMENT '用户分佣比例【新增 分享时分佣比例】',
  `music` tinyint(1) NULL DEFAULT 1 COMMENT '音乐是否开启 0：关闭 1：开启',
  `sound_effects` tinyint(1) NULL DEFAULT NULL COMMENT '音效是否开启 0：关闭 1：开启',
  `isset` tinyint(1) NULL DEFAULT 1 COMMENT '是否注销  1:未注销   2：注销',
  `money` decimal(7, 3) NULL DEFAULT 0.000 COMMENT '现金',
  `share_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '分享图片',
  `login_time` bigint(20) NULL DEFAULT NULL COMMENT '登录时间',
  `add_time` bigint(20) NULL DEFAULT NULL COMMENT '通过分享加入时间',
  `ad_number` int(11) NULL DEFAULT NULL COMMENT '观看广告数量',
  `revenue_model` smallint(1) UNSIGNED NULL DEFAULT 0 COMMENT '收益模式默认0 ',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '登录ip',
  `login_type` tinyint(1) NULL DEFAULT NULL COMMENT '登录方式 1：微信 2：抖音',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  `weixin_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '微信收款码',
  `alipay_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '支付宝收款码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `phone_2`(`phone`) USING BTREE,
  INDEX `invite_code`(`invite_code`) USING BTREE,
  INDEX `phone`(`pid`, `nick_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_user_ad_reward
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_ad_reward`;
CREATE TABLE `zc_user_ad_reward`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `ad_reward_num` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '总的奖励金额',
  `get_yestoday_reward` tinyint(1) UNSIGNED NULL DEFAULT 0,
  `get_yestoday_time` int(10) UNSIGNED NULL DEFAULT 0,
  `get_yestoday_money` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00,
  `latest_cal_time` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '广告奖励页面 最近一次计算待奖励时间戳 提交数据核验用',
  `add_time` datetime NULL DEFAULT NULL COMMENT 'latest_cal_time',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 73 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = FIXED;

-- ----------------------------
-- Table structure for zc_user_login
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_login`;
CREATE TABLE `zc_user_login`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0,
  `login_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '',
  `adv_num` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '登录当日看广告数量',
  `money` decimal(10, 3) UNSIGNED NULL DEFAULT 0.000 COMMENT '登录当日收益  收益存在厘的情形',
  `withdraw` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '当日提现',
  `invite_num` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '当日推荐人数',
  `m_type` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '机型 1安卓  2 ios  3平板 默认1',
  `m_version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '操作系统版本',
  `m_brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '品牌',
  `m_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '型号',
  `m_device_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '设备编码',
  `m_network` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '0未知 1WIFI 2流量  3VPN',
  `m_netserver` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '0未知 1中国移动 2中国联通 3中国电信',
  `m_simulator` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '是否模拟器 0 否  1是',
  `create_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `user_id_2`(`m_device_id`, `user_id`) USING BTREE,
  INDEX `ip`(`ip`, `user_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 9485 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_user_max_ad
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_max_ad`;
CREATE TABLE `zc_user_max_ad`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `ad_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '广告ID',
  `money` decimal(10, 3) UNSIGNED NULL DEFAULT 0.000 COMMENT '广告金额',
  `ad_time` int(10) UNSIGNED NULL DEFAULT 0,
  `ad_time_f` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户第一次触发广告金额大于等于0.4记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_user_relation
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_relation`;
CREATE TABLE `zc_user_relation`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT 0,
  `admin_id` int(11) NULL DEFAULT 0,
  `is_cheat` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '该账号是否是小号 仅按设备判断',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '',
  `m_type` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '机型 1安卓  2 ios  3平板 默认1',
  `m_version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '操作系统版本',
  `m_brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '品牌',
  `m_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '型号',
  `m_device_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT '' COMMENT '设备编码',
  `m_network` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '0未知 1WIFI 2流量  3VPN',
  `m_netserver` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '0未知 1中国移动 2中国联通 3中国电信',
  `m_simulator` smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '是否模拟器 0 否  1是',
  `bind_type` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '1注册绑定  2登录绑定',
  `create_time` bigint(20) NULL DEFAULT 0,
  `update_time` bigint(20) NULL DEFAULT 0,
  `delete_time` bigint(20) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `m_device_id`(`m_device_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1631 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_user_shop
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_shop`;
CREATE TABLE `zc_user_shop`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `shop_id` int(11) NULL DEFAULT NULL COMMENT '商品id',
  `income` decimal(7, 2) NULL DEFAULT NULL COMMENT '收益',
  `update` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
  `due_time` datetime NULL DEFAULT NULL COMMENT '到期时间',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除 0：否 1：是',
  `add_time` date NULL DEFAULT NULL COMMENT '添加时间',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '用户商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_withdrawal
-- ----------------------------
DROP TABLE IF EXISTS `zc_withdrawal`;
CREATE TABLE `zc_withdrawal`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `money` decimal(7, 2) NULL DEFAULT NULL COMMENT '提现金额',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '订单号',
  `real_money` decimal(7, 2) NULL DEFAULT NULL COMMENT '真实提现金额',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态： 1：审核中 2：已到账 3：驳回',
  `reply` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '驳回原因',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '类型 1：微信 2：支付宝',
  `adopt_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '后台回复人员',
  `add_time` date NULL DEFAULT NULL COMMENT '提现日期',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  `payment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '支付宝收款码',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `create_time`(`create_time`, `status`) USING BTREE,
  INDEX `add_time`(`add_time`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_withdrawal_money
-- ----------------------------
DROP TABLE IF EXISTS `zc_withdrawal_money`;
CREATE TABLE `zc_withdrawal_money`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `money` decimal(7, 1) NULL DEFAULT NULL COMMENT '提现金额',
  `day_num` int(10) NULL DEFAULT NULL COMMENT '每天提现次数',
  `sort` int(10) NULL DEFAULT 1 COMMENT '排序',
  `commission` int(10) NULL DEFAULT 0 COMMENT '手续费%',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_withdrawal_reward
-- ----------------------------
DROP TABLE IF EXISTS `zc_withdrawal_reward`;
CREATE TABLE `zc_withdrawal_reward`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `real_money` decimal(7, 2) NULL DEFAULT NULL COMMENT '真实提现金额',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态： 1：审核中 2：已到账 3已拒绝',
  `reply` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NULL DEFAULT NULL COMMENT '回复内容',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '类型 1：微信 2：支付宝',
  `adopt_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `add_time` date NULL DEFAULT NULL COMMENT '提现日期',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  `delete_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`, `add_time`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_croatian_ci COMMENT = '额外奖励记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zc_withdrawal_reward_third
-- ----------------------------
DROP TABLE IF EXISTS `zc_withdrawal_reward_third`;
CREATE TABLE `zc_withdrawal_reward_third`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0,
  `third_user_id` int(11) UNSIGNED NULL DEFAULT 0,
  `third_order_id` int(11) UNSIGNED NULL DEFAULT 0,
  `order_id` int(11) UNSIGNED NULL DEFAULT 0,
  `add_time` date NULL DEFAULT NULL COMMENT '提现日期',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = FIXED;

-- ----------------------------
-- Table structure for zc_withdrawal_third
-- ----------------------------
DROP TABLE IF EXISTS `zc_withdrawal_third`;
CREATE TABLE `zc_withdrawal_third`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NULL DEFAULT 0,
  `third_user_id` int(11) UNSIGNED NULL DEFAULT 0,
  `third_order_id` int(11) UNSIGNED NULL DEFAULT 0,
  `order_id` int(11) UNSIGNED NULL DEFAULT 0,
  `add_time` date NULL DEFAULT NULL COMMENT '提现日期',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = FIXED;

SET FOREIGN_KEY_CHECKS = 1;
