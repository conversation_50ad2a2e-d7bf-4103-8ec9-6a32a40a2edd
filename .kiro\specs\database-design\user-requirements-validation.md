# 用户管理表结构需求验证文档

## 概述

本文档验证用户管理相关表结构设计是否满足需求文档中的所有验收标准。

## 需求1验收标准验证

### 1.1 用户注册时创建用户基础信息记录

**需求**: WHEN 用户注册时 THEN 系统 SHALL 创建用户基础信息记录

**表设计支持**:
- ✅ `ct_users` 表包含完整的用户基础信息字段
- ✅ 支持手机号注册 (`phone` 字段，唯一索引)
- ✅ 支持微信注册 (`wechat_openid`, `wechat_unionid` 字段)
- ✅ 记录注册时间 (`created_at` 字段)
- ✅ 记录注册IP和设备信息 (`register_ip`, `register_device` 字段)

**字段映射**:
```sql
-- 用户注册基础信息
id, phone, password, nick_name, avatar, 
wechat_openid, wechat_unionid, status, 
register_ip, register_device, created_at
```

### 1.2 用户登录时记录登录信息和设备信息

**需求**: WHEN 用户登录时 THEN 系统 SHALL 记录登录信息和设备信息

**表设计支持**:
- ✅ `ct_user_logins` 表专门记录登录信息
- ✅ 支持多种登录类型 (`login_type` 字段)
- ✅ 详细的设备信息记录 (`device_type`, `device_model`, `device_id`, `os_version`)
- ✅ 网络环境信息 (`login_ip`, `network_type`, `user_agent`)
- ✅ 登录状态和失败原因 (`login_status`, `failure_reason`)
- ✅ 会话管理 (`session_id`, `logout_time`, `session_duration`)

**字段映射**:
```sql
-- 登录记录信息
user_id, login_type, login_ip, login_location,
device_type, device_model, device_id, os_version,
app_version, user_agent, network_type, screen_resolution,
login_status, failure_reason, session_id, created_at
```

### 1.3 用户实名认证时存储认证状态和信息

**需求**: WHEN 用户进行实名认证时 THEN 系统 SHALL 存储认证状态和信息

**表设计支持**:
- ✅ `ct_users` 表包含实名认证字段
- ✅ 认证状态标记 (`is_verified` 字段)
- ✅ 真实姓名存储 (`real_name` 字段)
- ✅ 身份证号存储 (`id_card` 字段)
- ✅ 支持认证状态查询索引 (`idx_is_verified`)

**字段映射**:
```sql
-- 实名认证信息
real_name, id_card, is_verified, updated_at
```

### 1.4 用户建立推荐关系时记录多级推荐关系

**需求**: WHEN 用户建立推荐关系时 THEN 系统 SHALL 记录多级推荐关系

**表设计支持**:
- ✅ `ct_users` 表包含推荐关系字段 (`referrer_id`, `referrer_level`)
- ✅ `ct_user_relations` 表详细管理推荐关系
- ✅ 支持多级推荐层级 (`relation_level` 字段)
- ✅ 记录邀请码使用 (`invite_code`, `invite_code_used` 字段)
- ✅ 推荐奖励管理 (`reward_points` 字段)
- ✅ 关系状态管理 (`is_active`, `status` 字段)

**字段映射**:
```sql
-- 用户表推荐关系
referrer_id, referrer_level, invite_code

-- 用户关系表详细信息
user_id, referrer_id, relation_type, relation_level,
bind_time, invite_code_used, reward_points, is_active
```

### 1.5 管理员用户登录时区分普通用户和管理员权限

**需求**: WHEN 管理员用户登录时 THEN 系统 SHALL 区分普通用户和管理员权限

**表设计支持**:
- ✅ `ct_admin_users` 表独立管理管理员账户
- ✅ 角色类型区分 (`role_type` 字段)
- ✅ 权限列表管理 (`permissions` 字段，JSON格式)
- ✅ 超级管理员标记 (`is_super_admin` 字段)
- ✅ 管理员状态管理 (`status` 字段)
- ✅ 管理员层级关系 (`created_by` 字段)

**字段映射**:
```sql
-- 管理员用户信息
username, password, role_type, permissions,
is_super_admin, status, created_by
```

## 扩展功能支持验证

### 积分系统支持
- ✅ 用户总积分记录 (`total_points` 字段)
- ✅ 可用积分管理 (`available_points` 字段)
- ✅ 推荐奖励积分 (`reward_points` 字段)

### 用户状态管理
- ✅ 用户状态控制 (`status` 字段: 0-禁用，1-正常，2-待审核)
- ✅ 软删除机制 (`is_deleted`, `deleted_at` 字段)
- ✅ 管理员状态控制 (`status` 字段: 0-禁用，1-正常，2-锁定)

### 安全性支持
- ✅ 密码加密存储 (MD5加密)
- ✅ 登录失败记录和原因
- ✅ 设备信息跟踪
- ✅ IP地址记录和地理位置

### 审计和日志
- ✅ 创建和更新时间记录
- ✅ 登录历史完整记录
- ✅ 推荐关系绑定记录
- ✅ 管理员操作记录

## 性能优化验证

### 索引设计
- ✅ 主键索引：所有表的 `id` 字段
- ✅ 唯一索引：`phone`, `invite_code`, `username`
- ✅ 外键索引：所有关联字段
- ✅ 业务索引：状态、时间、设备等常用查询字段

### 查询优化
- ✅ 支持用户登录查询 (phone索引)
- ✅ 支持推荐关系查询 (referrer_id索引)
- ✅ 支持状态筛选查询 (status索引)
- ✅ 支持时间范围查询 (created_at索引)

## 数据完整性验证

### 外键约束
- ✅ 用户推荐关系约束
- ✅ 登录记录用户约束
- ✅ 用户关系双向约束
- ✅ 管理员创建关系约束

### 唯一性约束
- ✅ 手机号唯一性
- ✅ 邀请码唯一性
- ✅ 管理员用户名唯一性
- ✅ 用户关系唯一性

### 业务规则约束
- ✅ 状态值范围控制
- ✅ 必填字段非空约束
- ✅ 默认值设置
- ✅ 软删除机制

## 扩展性验证

### 字段扩展
- ✅ 预留了足够的字段长度
- ✅ JSON字段支持灵活配置
- ✅ 时间字段支持未来功能

### 表结构扩展
- ✅ 支持新增字段而不影响现有功能
- ✅ 支持新增索引优化查询
- ✅ 支持分区策略应对大数据量

## 总结

用户管理相关表结构设计完全满足需求文档中的所有验收标准：

1. ✅ **用户注册功能**: 完整支持用户基础信息记录
2. ✅ **登录记录功能**: 详细记录登录信息和设备信息
3. ✅ **实名认证功能**: 支持认证状态和信息存储
4. ✅ **推荐关系功能**: 完整的多级推荐关系管理
5. ✅ **权限管理功能**: 独立的管理员用户体系

此外，表结构设计还考虑了：
- 🚀 **性能优化**: 合理的索引设计
- 🔒 **数据安全**: 密码加密和访问控制
- 📊 **审计日志**: 完整的操作记录
- 🔧 **扩展性**: 支持未来功能扩展
- 🛡️ **数据完整性**: 完善的约束机制

表结构设计符合数据库规范化原则，能够支持次推应用的用户管理需求。