-- =============================================
-- 次推应用 - 放水线索相关表结构设计
-- 创建时间: 2025-01-08
-- 描述: 包含放水线索表、线索反馈表和线索统计表
-- =============================================

-- 设置字符集和存储引擎
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 放水线索表 (ct_water_clues)
-- 用途: 存储用户提交的放水线索信息
-- 需求: 3.1, 3.2, 3.3, 3.4, 3.5
-- =============================================
DROP TABLE IF EXISTS `ct_water_clues`;
CREATE TABLE `ct_water_clues` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '线索ID，主键',
  `app_id` int(11) NOT NULL COMMENT '关联APP ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '提交用户ID',
  `title` varchar(200) NOT NULL COMMENT '线索标题',
  `water_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '放水金额',
  `water_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '放水类型：1-新人福利，2-活动奖励，3-日常任务，4-其他',
  `description` text NOT NULL COMMENT '线索详细描述',
  `water_time` datetime NOT NULL COMMENT '放水时间',
  `duration_minutes` int(11) DEFAULT NULL COMMENT '持续时长（分钟）',
  `participant_count` int(11) DEFAULT 0 COMMENT '参与人数',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率（百分比）',
  `difficulty_level` tinyint(1) DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `device_requirement` varchar(100) DEFAULT NULL COMMENT '设备要求',
  `network_requirement` varchar(50) DEFAULT NULL COMMENT '网络要求',
  `region_limitation` varchar(200) DEFAULT NULL COMMENT '地区限制',
  `age_requirement` varchar(50) DEFAULT NULL COMMENT '年龄要求',
  `other_requirements` text DEFAULT NULL COMMENT '其他要求说明',
  `submitter_device` varchar(200) DEFAULT NULL COMMENT '提交人设备信息',
  `submitter_ip` varchar(50) DEFAULT NULL COMMENT '提交人IP地址',
  `submitter_location` varchar(100) DEFAULT NULL COMMENT '提交人地理位置',
  `verification_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '验证状态：0-未验证，1-已验证，2-验证失败',
  `verification_count` int(11) DEFAULT 0 COMMENT '验证次数',
  `success_verification_count` int(11) DEFAULT 0 COMMENT '成功验证次数',
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `points_awarded` int(11) DEFAULT 0 COMMENT '已奖励积分',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `share_count` int(11) NOT NULL DEFAULT 0 COMMENT '分享次数',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评论次数',
  `collect_count` int(11) NOT NULL DEFAULT 0 COMMENT '收藏次数',
  `hot_score` decimal(8,2) DEFAULT 0.00 COMMENT '热度分数',
  `quality_score` decimal(5,2) DEFAULT 0.00 COMMENT '质量评分',
  `is_hot` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `is_recommended` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐：0-否，1-是',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶：0-否，1-是',
  `top_expire_time` datetime DEFAULT NULL COMMENT '置顶过期时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-删除，1-正常，2-隐藏',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_water_time` (`water_time`),
  KEY `idx_water_amount` (`water_amount`),
  KEY `idx_water_type` (`water_type`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_verification_status` (`verification_status`),
  KEY `idx_status` (`status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_recommended` (`is_recommended`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_hot_score` (`hot_score`),
  KEY `idx_quality_score` (`quality_score`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_like_count` (`like_count`),
  CONSTRAINT `fk_water_clues_app_id` FOREIGN KEY (`app_id`) REFERENCES `ct_apps` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_water_clues_user_id` FOREIGN KEY (`user_id`) REFERENCES `ct_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_water_clues_audit_user` FOREIGN KEY (`audit_user_id`) REFERENCES `ct_admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='放水线索表';

-- =============================================
-- 2. 线索反馈表 (ct_clue_feedbacks)
-- 用途: 存储用户对线索的反馈和验证信息
-- 需求: 3.2, 3.3, 3.5
-- =============================================
DROP TABLE IF EXISTS `ct_clue_feedbacks`;
CREATE TABLE `ct_clue_feedbacks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '反馈ID，主键',
  `clue_id` bigint(20) unsigned NOT NULL COMMENT '线索ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '反馈用户ID',
  `feedback_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '反馈类型：1-验证成功，2-验证失败，3-举报虚假，4-补充信息，5-其他',
  `is_successful` tinyint(1) DEFAULT NULL COMMENT '是否成功：0-失败，1-成功，NULL-未知',
  `actual_amount` decimal(10,2) DEFAULT NULL COMMENT '实际获得金额',
  `time_spent` int(11) DEFAULT NULL COMMENT '花费时间（分钟）',
  `difficulty_rating` tinyint(1) DEFAULT NULL COMMENT '难度评分：1-很简单，2-简单，3-中等，4-困难，5-很困难',
  `accuracy_rating` tinyint(1) DEFAULT NULL COMMENT '准确性评分：1-很不准确，2-不准确，3-一般，4-准确，5-很准确',
  `overall_rating` tinyint(1) DEFAULT NULL COMMENT '总体评分：1-很差，2-差，3-一般，4-好，5-很好',
  `feedback_content` text DEFAULT NULL COMMENT '反馈内容详情',
  `additional_info` text DEFAULT NULL COMMENT '补充信息',
  `device_info` varchar(200) DEFAULT NULL COMMENT '使用设备信息',
  `network_info` varchar(50) DEFAULT NULL COMMENT '网络环境信息',
  `location_info` varchar(100) DEFAULT NULL COMMENT '地理位置信息',
  `screenshot_count` int(11) DEFAULT 0 COMMENT '截图数量',
  `evidence_files` text DEFAULT NULL COMMENT '证据文件列表，JSON格式',
  `ip_address` varchar(50) DEFAULT NULL COMMENT '反馈IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理信息',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否匿名反馈：0-否，1-是',
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `points_awarded` int(11) DEFAULT 0 COMMENT '已奖励积分',
  `helpful_count` int(11) NOT NULL DEFAULT 0 COMMENT '有用评价数',
  `unhelpful_count` int(11) NOT NULL DEFAULT 0 COMMENT '无用评价数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-删除，1-正常，2-隐藏',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_clue_id` (`clue_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_feedback_type` (`feedback_type`),
  KEY `idx_is_successful` (`is_successful`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_overall_rating` (`overall_rating`),
  KEY `idx_helpful_count` (`helpful_count`),
  CONSTRAINT `fk_clue_feedbacks_clue_id` FOREIGN KEY (`clue_id`) REFERENCES `ct_water_clues` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_clue_feedbacks_user_id` FOREIGN KEY (`user_id`) REFERENCES `ct_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_clue_feedbacks_audit_user` FOREIGN KEY (`audit_user_id`) REFERENCES `ct_admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索反馈表';

-- =============================================
-- 3. 线索统计表 (ct_clue_statistics)
-- 用途: 存储线索的统计数据和分析信息
-- 需求: 3.4, 3.5
-- =============================================
DROP TABLE IF EXISTS `ct_clue_statistics`;
CREATE TABLE `ct_clue_statistics` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '统计ID，主键',
  `clue_id` bigint(20) unsigned NOT NULL COMMENT '线索ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '统计类型：1-日统计，2-周统计，3-月统计',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `share_count` int(11) NOT NULL DEFAULT 0 COMMENT '分享次数',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评论次数',
  `collect_count` int(11) NOT NULL DEFAULT 0 COMMENT '收藏次数',
  `feedback_count` int(11) NOT NULL DEFAULT 0 COMMENT '反馈次数',
  `success_feedback_count` int(11) NOT NULL DEFAULT 0 COMMENT '成功反馈次数',
  `failed_feedback_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败反馈次数',
  `report_count` int(11) NOT NULL DEFAULT 0 COMMENT '举报次数',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率（百分比）',
  `average_rating` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  `average_amount` decimal(10,2) DEFAULT 0.00 COMMENT '平均获得金额',
  `total_amount` decimal(12,2) DEFAULT 0.00 COMMENT '总获得金额',
  `average_time_spent` int(11) DEFAULT 0 COMMENT '平均花费时间（分钟）',
  `unique_user_count` int(11) NOT NULL DEFAULT 0 COMMENT '独立用户数',
  `new_user_count` int(11) NOT NULL DEFAULT 0 COMMENT '新用户数',
  `return_user_count` int(11) NOT NULL DEFAULT 0 COMMENT '回访用户数',
  `conversion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '转化率（查看到反馈）',
  `engagement_score` decimal(8,2) DEFAULT 0.00 COMMENT '参与度分数',
  `quality_score` decimal(5,2) DEFAULT 0.00 COMMENT '质量分数',
  `hot_score` decimal(8,2) DEFAULT 0.00 COMMENT '热度分数',
  `trend_score` decimal(8,2) DEFAULT 0.00 COMMENT '趋势分数',
  `rank_position` int(11) DEFAULT 0 COMMENT '排名位置',
  `category_rank` int(11) DEFAULT 0 COMMENT '分类内排名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_clue_date_type` (`clue_id`, `stat_date`, `stat_type`),
  KEY `idx_clue_id` (`clue_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_stat_type` (`stat_type`),
  KEY `idx_success_rate` (`success_rate`),
  KEY `idx_hot_score` (`hot_score`),
  KEY `idx_quality_score` (`quality_score`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_engagement_score` (`engagement_score`),
  KEY `idx_rank_position` (`rank_position`),
  CONSTRAINT `fk_clue_statistics_clue_id` FOREIGN KEY (`clue_id`) REFERENCES `ct_water_clues` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索统计表';

-- =============================================
-- 索引优化说明
-- =============================================
/*
1. ct_water_clues表索引设计：
   - 主键索引：id（自动创建）
   - 外键索引：app_id、user_id、audit_user_id（关联查询）
   - 业务索引：water_time（时间排序）、water_amount（金额排序）、water_type（类型筛选）
   - 状态索引：audit_status、verification_status、status（状态筛选）
   - 推荐索引：is_hot、is_recommended、is_top（推荐内容查询）
   - 评分索引：hot_score、quality_score（排序查询）
   - 统计索引：view_count、like_count（热度排序）
   - 时间索引：created_at（创建时间排序）

2. ct_clue_feedbacks表索引设计：
   - 主键索引：id（自动创建）
   - 外键索引：clue_id、user_id、audit_user_id（关联查询）
   - 业务索引：feedback_type（反馈类型筛选）、is_successful（成功状态筛选）
   - 状态索引：audit_status、status（状态筛选）
   - 评分索引：overall_rating（评分排序）
   - 统计索引：helpful_count（有用评价排序）
   - 时间索引：created_at（创建时间排序）

3. ct_clue_statistics表索引设计：
   - 主键索引：id（自动创建）
   - 唯一索引：clue_id + stat_date + stat_type（防止重复统计）
   - 外键索引：clue_id（关联查询）
   - 时间索引：stat_date（日期查询）
   - 类型索引：stat_type（统计类型筛选）
   - 评分索引：success_rate、hot_score、quality_score（排序查询）
   - 统计索引：view_count、engagement_score、rank_position（排序查询）
*/

-- =============================================
-- 触发器设计（用于自动更新统计数据）
-- =============================================

-- 创建触发器：线索查看次数更新时同步到统计表
DELIMITER $$
CREATE TRIGGER `tr_water_clues_view_update` 
AFTER UPDATE ON `ct_water_clues`
FOR EACH ROW
BEGIN
    IF NEW.view_count != OLD.view_count THEN
        INSERT INTO `ct_clue_statistics` (
            `clue_id`, `stat_date`, `stat_type`, `view_count`, `created_at`, `updated_at`
        ) VALUES (
            NEW.id, CURDATE(), 1, NEW.view_count, NOW(), NOW()
        ) ON DUPLICATE KEY UPDATE 
            `view_count` = NEW.view_count,
            `updated_at` = NOW();
    END IF;
END$$
DELIMITER ;

-- 创建触发器：反馈提交时更新线索统计
DELIMITER $$
CREATE TRIGGER `tr_clue_feedback_insert` 
AFTER INSERT ON `ct_clue_feedbacks`
FOR EACH ROW
BEGIN
    -- 更新线索表的统计字段
    UPDATE `ct_water_clues` SET 
        `comment_count` = `comment_count` + 1,
        `verification_count` = `verification_count` + 1,
        `success_verification_count` = CASE 
            WHEN NEW.is_successful = 1 THEN `success_verification_count` + 1 
            ELSE `success_verification_count` 
        END,
        `updated_at` = NOW()
    WHERE `id` = NEW.clue_id;
    
    -- 更新统计表
    INSERT INTO `ct_clue_statistics` (
        `clue_id`, `stat_date`, `stat_type`, `feedback_count`, 
        `success_feedback_count`, `failed_feedback_count`, `created_at`, `updated_at`
    ) VALUES (
        NEW.clue_id, CURDATE(), 1, 1,
        CASE WHEN NEW.is_successful = 1 THEN 1 ELSE 0 END,
        CASE WHEN NEW.is_successful = 0 THEN 1 ELSE 0 END,
        NOW(), NOW()
    ) ON DUPLICATE KEY UPDATE 
        `feedback_count` = `feedback_count` + 1,
        `success_feedback_count` = CASE 
            WHEN NEW.is_successful = 1 THEN `success_feedback_count` + 1 
            ELSE `success_feedback_count` 
        END,
        `failed_feedback_count` = CASE 
            WHEN NEW.is_successful = 0 THEN `failed_feedback_count` + 1 
            ELSE `failed_feedback_count` 
        END,
        `updated_at` = NOW();
END$$
DELIMITER ;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;