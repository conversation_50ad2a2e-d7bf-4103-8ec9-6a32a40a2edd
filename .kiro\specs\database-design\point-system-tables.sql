-- =============================================
-- 积分和奖励系统表结构设计
-- 创建时间: 2025-01-08
-- 描述: 支持用户积分管理、奖励规则配置和积分变动记录
-- =============================================

-- 积分记录表
DROP TABLE IF EXISTS `ct_point_records`;
CREATE TABLE `ct_point_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `point_change` int(11) NOT NULL COMMENT '积分变动数量(正数为增加，负数为扣除)',
  `point_balance` int(11) NOT NULL DEFAULT 0 COMMENT '变动后积分余额',
  `change_type` varchar(50) NOT NULL COMMENT '变动类型(evaluation_submit:评测提交, clue_submit:线索提交, audit_pass:审核通过, audit_reject:审核拒绝, manual_adjust:手动调整)',
  `change_reason` varchar(200) DEFAULT NULL COMMENT '变动原因描述',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联业务类型(evaluation:评测报告, clue:放水线索)',
  `related_id` bigint(20) DEFAULT NULL COMMENT '关联业务ID',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID(系统自动为NULL，管理员操作记录管理员ID)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '记录状态(0:无效, 1:有效)',
  `remark` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_related` (`related_type`, `related_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分记录表';

-- 积分规则表
DROP TABLE IF EXISTS `ct_point_rules`;
CREATE TABLE `ct_point_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `rule_code` varchar(50) NOT NULL COMMENT '规则代码(evaluation_submit, clue_submit, daily_login等)',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `point_value` int(11) NOT NULL COMMENT '积分数值(正数为奖励，负数为扣除)',
  `trigger_condition` varchar(200) DEFAULT NULL COMMENT '触发条件描述',
  `daily_limit` int(11) DEFAULT NULL COMMENT '每日限制次数(NULL表示无限制)',
  `total_limit` int(11) DEFAULT NULL COMMENT '总限制次数(NULL表示无限制)',
  `valid_start_date` date DEFAULT NULL COMMENT '规则生效开始日期',
  `valid_end_date` date DEFAULT NULL COMMENT '规则生效结束日期',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '规则优先级(数字越大优先级越高)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '规则状态(0:禁用, 1:启用)',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_valid_date` (`valid_start_date`, `valid_end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分规则表';

-- 奖励配置表
DROP TABLE IF EXISTS `ct_reward_configs`;
CREATE TABLE `ct_reward_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_code` varchar(50) NOT NULL COMMENT '配置代码',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型(point_exchange:积分兑换, level_reward:等级奖励, activity_reward:活动奖励)',
  `reward_type` varchar(50) NOT NULL COMMENT '奖励类型(point:积分, virtual_item:虚拟物品, real_item:实物)',
  `reward_value` varchar(200) NOT NULL COMMENT '奖励内容(积分数量或物品信息JSON)',
  `required_points` int(11) DEFAULT NULL COMMENT '所需积分数量',
  `required_level` int(11) DEFAULT NULL COMMENT '所需用户等级',
  `exchange_limit` int(11) DEFAULT NULL COMMENT '兑换限制次数(NULL表示无限制)',
  `daily_limit` int(11) DEFAULT NULL COMMENT '每日兑换限制',
  `stock_quantity` int(11) DEFAULT NULL COMMENT '库存数量(NULL表示无限制)',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配置状态(0:禁用, 1:启用)',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '生效开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '生效结束时间',
  `description` text COMMENT '配置描述',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_code` (`config_code`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_reward_type` (`reward_type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='奖励配置表';

-- 用户积分汇总表(用于快速查询用户当前积分)
DROP TABLE IF EXISTS `ct_user_points`;
CREATE TABLE `ct_user_points` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `total_points` int(11) NOT NULL DEFAULT 0 COMMENT '总积分',
  `available_points` int(11) NOT NULL DEFAULT 0 COMMENT '可用积分',
  `frozen_points` int(11) NOT NULL DEFAULT 0 COMMENT '冻结积分',
  `total_earned` int(11) NOT NULL DEFAULT 0 COMMENT '累计获得积分',
  `total_consumed` int(11) NOT NULL DEFAULT 0 COMMENT '累计消耗积分',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '用户等级',
  `level_progress` int(11) NOT NULL DEFAULT 0 COMMENT '当前等级进度积分',
  `next_level_points` int(11) DEFAULT NULL COMMENT '升级到下一等级所需积分',
  `last_point_time` timestamp NULL DEFAULT NULL COMMENT '最后积分变动时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  KEY `idx_total_points` (`total_points`),
  KEY `idx_level` (`level`),
  KEY `idx_last_point_time` (`last_point_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分汇总表';

-- 积分兑换记录表
DROP TABLE IF EXISTS `ct_point_exchanges`;
CREATE TABLE `ct_point_exchanges` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '兑换记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `reward_config_id` int(11) NOT NULL COMMENT '奖励配置ID',
  `exchange_points` int(11) NOT NULL COMMENT '兑换消耗积分',
  `reward_content` varchar(500) NOT NULL COMMENT '奖励内容描述',
  `exchange_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '兑换状态(0:待处理, 1:已发放, 2:已取消, 3:发放失败)',
  `exchange_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '兑换时间',
  `process_time` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `process_by` bigint(20) DEFAULT NULL COMMENT '处理人ID',
  `remark` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_reward_config_id` (`reward_config_id`),
  KEY `idx_exchange_status` (`exchange_status`),
  KEY `idx_exchange_time` (`exchange_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分兑换记录表';