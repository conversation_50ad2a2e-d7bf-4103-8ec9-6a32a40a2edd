# APP和评测相关表结构设计

## 1. APP分类表 (ct_app_categories)

```sql
CREATE TABLE `ct_app_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标URL',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重，数值越大越靠前',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP分类表';
```

## 2. APP信息表 (ct_apps)

```sql
CREATE TABLE `ct_apps` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'APP ID',
  `name` varchar(100) NOT NULL COMMENT 'APP名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `logo_url` varchar(255) DEFAULT NULL COMMENT 'APP Logo URL',
  `package_name` varchar(100) DEFAULT NULL COMMENT '包名',
  `version` varchar(20) DEFAULT NULL COMMENT '版本号',
  `download_url` varchar(500) DEFAULT NULL COMMENT '下载链接',
  `register_url` varchar(500) DEFAULT NULL COMMENT '注册链接',
  `description` text COMMENT 'APP描述',
  `rating` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分(0-5分)',
  `rating_count` int(11) DEFAULT 0 COMMENT '评分人数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `run_mode` varchar(50) DEFAULT NULL COMMENT '运行模式：挂机、手动、半自动等',
  `newcomer_benefit` varchar(200) DEFAULT NULL COMMENT '新人福利描述',
  `withdraw_threshold` decimal(10,2) DEFAULT 0.00 COMMENT '提现门槛金额',
  `guarantee_amount` decimal(10,2) DEFAULT 0.00 COMMENT '顶包金额',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门：1-是，0-否',
  `is_recommended` tinyint(1) DEFAULT 0 COMMENT '是否推荐：1-是，0-否',
  `is_water_available` tinyint(1) DEFAULT 0 COMMENT '是否有放水：1-是，0-否',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-下架，2-审核中',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status_hot` (`status`, `is_hot`),
  KEY `idx_status_recommended` (`status`, `is_recommended`),
  KEY `idx_rating` (`rating`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_apps_category` FOREIGN KEY (`category_id`) REFERENCES `ct_app_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP信息表';
```

## 3. 评测报告表 (ct_evaluation_reports)

```sql
CREATE TABLE `ct_evaluation_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `app_id` int(11) NOT NULL COMMENT 'APP ID',
  `user_id` int(11) NOT NULL COMMENT '提交用户ID',
  `title` varchar(200) DEFAULT NULL COMMENT '报告标题',
  `rating` decimal(3,2) NOT NULL COMMENT '用户评分(0-5分)',
  `app_type` varchar(50) DEFAULT NULL COMMENT 'APP类型',
  `run_mode` varchar(50) DEFAULT NULL COMMENT '运行模式',
  `newcomer_benefit` varchar(200) DEFAULT NULL COMMENT '新人福利',
  `withdraw_threshold` decimal(10,2) DEFAULT 0.00 COMMENT '提现门槛',
  `guarantee_amount` decimal(10,2) DEFAULT 0.00 COMMENT '顶包金额',
  `test_count` int(11) DEFAULT 0 COMMENT '测试条数',
  `total_earnings` decimal(10,2) DEFAULT 0.00 COMMENT '总收益',
  `test_duration` int(11) DEFAULT 0 COMMENT '测试时长(分钟)',
  `test_device` varchar(100) DEFAULT NULL COMMENT '测试设备',
  `evaluator_name` varchar(50) DEFAULT NULL COMMENT '测评人姓名',
  `evaluation_date` date DEFAULT NULL COMMENT '测评日期',
  `earnings_detail` text COMMENT '收益明细JSON格式',
  `report_content` text COMMENT '测评报告内容',
  `audit_status` tinyint(1) DEFAULT 0 COMMENT '审核状态：0-待审核，1-通过，2-拒绝',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `points_awarded` int(11) DEFAULT 0 COMMENT '已奖励积分',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_rating` (`rating`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_reports_app` FOREIGN KEY (`app_id`) REFERENCES `ct_apps` (`id`),
  CONSTRAINT `fk_reports_user` FOREIGN KEY (`user_id`) REFERENCES `ct_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评测报告表';
```

## 4. 评测数据详情表 (ct_evaluation_details)

```sql
CREATE TABLE `ct_evaluation_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '详情ID',
  `report_id` int(11) NOT NULL COMMENT '评测报告ID',
  `detail_type` varchar(50) NOT NULL COMMENT '详情类型：screenshot-截图，earning-收益明细，test_data-测试数据',
  `title` varchar(200) DEFAULT NULL COMMENT '详情标题',
  `content` text COMMENT '详情内容',
  `file_url` varchar(500) DEFAULT NULL COMMENT '相关文件URL',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据JSON格式',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`),
  KEY `idx_detail_type` (`detail_type`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_details_report` FOREIGN KEY (`report_id`) REFERENCES `ct_evaluation_reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评测数据详情表';
```

## 索引说明

### ct_app_categories 表索引
- `PRIMARY KEY (id)`: 主键索引
- `idx_status_sort (status, sort_order)`: 复合索引，用于按状态和排序查询分类
- `idx_name (name)`: 分类名称索引，用于名称搜索

### ct_apps 表索引
- `PRIMARY KEY (id)`: 主键索引
- `idx_category (category_id)`: 分类索引，用于按分类查询APP
- `idx_status_hot (status, is_hot)`: 复合索引，用于查询热门APP
- `idx_status_recommended (status, is_recommended)`: 复合索引，用于查询推荐APP
- `idx_rating (rating)`: 评分索引，用于按评分排序
- `idx_name (name)`: APP名称索引，用于名称搜索

### ct_evaluation_reports 表索引
- `PRIMARY KEY (id)`: 主键索引
- `idx_app_id (app_id)`: APP索引，用于查询某个APP的所有评测
- `idx_user_id (user_id)`: 用户索引，用于查询某个用户的所有评测
- `idx_audit_status (audit_status)`: 审核状态索引，用于审核管理
- `idx_create_time (create_time)`: 创建时间索引，用于时间排序
- `idx_rating (rating)`: 评分索引，用于按评分筛选
- `idx_status (status)`: 状态索引，用于过滤删除数据

### ct_evaluation_details 表索引
- `PRIMARY KEY (id)`: 主键索引
- `idx_report_id (report_id)`: 报告索引，用于查询某个报告的所有详情
- `idx_detail_type (detail_type)`: 详情类型索引，用于按类型查询
- `idx_sort_order (sort_order)`: 排序索引，用于详情排序

## 外键约束说明

1. `ct_apps.category_id` → `ct_app_categories.id`: APP分类关联
2. `ct_evaluation_reports.app_id` → `ct_apps.id`: 评测报告关联APP
3. `ct_evaluation_reports.user_id` → `ct_users.id`: 评测报告关联用户
4. `ct_evaluation_details.report_id` → `ct_evaluation_reports.id`: 评测详情关联报告(级联删除)

## 字段设计说明

### 状态字段统一设计
- 使用 `tinyint(1)` 类型存储状态
- 1表示正常/启用，0表示删除/禁用，2表示特殊状态(如审核中)

### 时间字段统一设计
- 使用 `datetime` 类型而非时间戳
- 创建时间默认 `CURRENT_TIMESTAMP`
- 更新时间默认 `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`

### 金额字段设计
- 使用 `decimal(10,2)` 类型存储金额，精确到分
- 避免浮点数精度问题

### JSON字段使用
- `extra_data` 字段使用JSON类型存储扩展数据
- `earnings_detail` 字段存储收益明细的结构化数据

## 业务逻辑说明

### 评测报告审核流程
1. 用户提交评测报告，`audit_status` 默认为0(待审核)
2. 管理员审核通过后，`audit_status` 设为1，发放积分奖励
3. 审核拒绝时，`audit_status` 设为2，记录拒绝原因

### APP评分计算
- `ct_apps.rating` 字段存储平均评分
- `ct_apps.rating_count` 字段存储评分人数
- 每次新增评测报告时，需要重新计算平均评分

### 文件关联处理
- 评测报告的截图通过 `ct_evaluation_details` 表存储
- `detail_type` 为 'screenshot' 的记录存储截图信息
- `file_url` 字段存储文件访问路径