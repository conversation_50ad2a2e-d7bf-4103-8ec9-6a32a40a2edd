<?php
declare(strict_types=1);

use App\Http\Controllers\Api\V1\Wap;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//广告奖励回调地址
Route::namespace('Api')->prefix('ad_reward')->group(function(){
    Route::get('gromore_x4gjvqcdgu',[Wap\User\UserController::class,'rewardGromoreCallback']);
    Route::get('gdt_cvwfn8tvwu',[Wap\User\UserController::class,'rewardGdtCallback']);

    Route::get('gromore',[Wap\User\UserController::class,'rewardGromoreCallback']);
    Route::get('gdt',[Wap\User\UserController::class,'rewardGdtCallback']);
    Route::get('ks',[Wap\User\UserController::class,'rewardKsCallback']);
});


Route::namespace('Api')->group(function () {
    Route::get('captcha',[Wap\Common\CaptchaController::class,'getCaptcha']);
    Route::post('Login/loginSms',[Wap\User\UserController::class,'loginSms']);
    Route::post('user/register',[Wap\User\UserController::class,'register']);
    Route::post('user/reg_h5',[Wap\User\UserController::class,'regH5']);

    Route::get('index',[Wap\Index\IndexController::class,'index']);
    Route::get('game/list',[Wap\Index\IndexController::class,'gameList']);
    Route::get('app/download',[Wap\Index\IndexController::class,'appDownload']);
    Route::get('game/download',[Wap\Index\IndexController::class,'gameDownload']);
    Route::match(['get', 'post'], 'Config/getProtocol',[Wap\Index\IndexController::class,'getProtocol']);

    Route::post('Withdrawal/adRewardInfoByUser',[Wap\User\UserController::class,'adRewardInfoByUser']);//查看用户广告奖励数据


    Route::post('user/regFromThird',[Wap\User\UserController::class,'regFromThird']);//第三方注册
    Route::post('user/updateFromThird',[Wap\User\UserController::class,'updateFromThird']);//第三方更新
    Route::post('user/bindFromThird',[Wap\User\UserController::class,'bindFromThird']);//第三方绑定账号

    Route::post('user/withdrawNotify',[Wap\User\UserController::class,'withdrawNotify']);
    Route::post('user/withdrawRewardNotify',[Wap\User\UserController::class,'withdrawRewardNotify']);

});

Route::namespace('Api')->middleware('userCanEmptyLogin')->group(function(){
    Route::get('services',[Wap\Index\IndexController::class,'getServices']);
});

Route::namespace('Api')->middleware('userLogin')->group(function(){

    Route::post('rewardGromore_a2cpb7byce',[Wap\User\UserController::class,'rewardGromore']);
    Route::post('rewardGdt_c2pb31ucg6',[Wap\User\UserController::class,'rewardGdt']);
    Route::post('rewardKs',[Wap\User\UserController::class,'rewardKs']);
    
    
    Route::post('rewardGromore',[Wap\User\UserController::class,'rewardGromore']);
    Route::post('rewardGdt',[Wap\User\UserController::class,'rewardGdt']);
    Route::post('rewardKs',[Wap\User\UserController::class,'rewardKs']);

    Route::get('moneyAndAn/data',[Wap\Index\IndexController::class,'moneyAndAn']);//没用到
    Route::get('Config/getIcon',[Wap\Index\IndexController::class,'getIcon']);
    Route::get('Config/getOdds',[Wap\Index\IndexController::class,'getOdds']);
    Route::get('Withdrawal/selectMoney',[Wap\Index\IndexController::class,'selectMoney']);//获取提现金额数组 含URL信息
    Route::get('Withdrawal/WithdrawalList',[Wap\User\UserController::class,'withdrawList']);//获取提现列表
    Route::match(['get', 'post'], 'RedPacket/MoneyList',[Wap\User\UserController::class,'moneyList']);//获取红包金额列表
    Route::post('Withdrawal/WithdrawalAdd',[Wap\User\UserController::class,'withdrawAdd']);//提现申请
    Route::get('user/info',[Wap\User\UserController::class,'getUserInfo']);
    Route::get('User/getUser',[Wap\User\UserController::class,'getUser']);
    Route::post('RedPacket/canAd',[Wap\User\UserController::class,'canAd']);//是否可以查看广告
    Route::post('RedPacket/PacketDescribe',[Wap\User\UserController::class,'packetDescribe']);//红包描述
    Route::post('RedPacket/collectPacketOne',[Wap\User\UserController::class,'collectPacketOne']);//领取红包
    Route::post('Uplaode/uplaodImage',[Wap\Common\UploadController::class,'upload']);
    Route::post('Withdrawal/bindCard',[Wap\User\UserController::class,'bindCard']);//用户绑定微信收款码
    Route::get('Withdrawal/info',[Wap\User\UserController::class,'withdrawInfo']);//用户微信收款二维码及提现说明

    Route::get('Withdrawal/adReward',[Wap\User\UserController::class,'adReward']);//用户广告奖励数据
    Route::post('Withdrawal/adRewardAdd',[Wap\User\UserController::class,'adRewardAdd']);//用户提交广告奖励数据
    Route::get('Withdrawal/adRewardList',[Wap\User\UserController::class,'adRewardList']);//用户广告奖励列表


});
