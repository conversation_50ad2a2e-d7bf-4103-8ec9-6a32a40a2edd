-- APP和评测相关表结构测试SQL脚本

-- 1. 创建测试数据库（如果需要）
-- CREATE DATABASE IF NOT EXISTS citui_test DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
-- USE citui_test;

-- 2. 创建APP分类表测试数据
INSERT INTO `ct_app_categories` (`name`, `description`, `icon`, `sort_order`, `status`) VALUES
('游戏类', '各种游戏APP，包括休闲游戏、策略游戏等', '/icons/game.png', 100, 1),
('任务类', '做任务赚钱的APP，包括问卷调查、数据录入等', '/icons/task.png', 90, 1),
('购物类', '购物返利APP，包括淘宝客、拼多多等', '/icons/shopping.png', 80, 1),
('视频类', '看视频赚钱APP，包括短视频、直播等', '/icons/video.png', 70, 1),
('阅读类', '阅读赚钱APP，包括新闻阅读、小说阅读等', '/icons/reading.png', 60, 1);

-- 3. 创建APP信息表测试数据
INSERT INTO `ct_apps` (`name`, `category_id`, `logo_url`, `package_name`, `version`, `download_url`, `register_url`, `description`, `rating`, `rating_count`, `download_count`, `run_mode`, `newcomer_benefit`, `withdraw_threshold`, `guarantee_amount`, `is_hot`, `is_recommended`, `is_water_available`, `status`) VALUES
('开心消消乐', 1, '/logos/xxl.png', 'com.tencent.happyeliminate', '1.2.3', 'https://download.com/xxl', 'https://register.com/xxl', '经典三消游戏，轻松赚钱', 4.5, 128, 5420, '半自动', '新人注册送5元', 10.00, 50.00, 1, 1, 1, 1),
('问卷星', 2, '/logos/wjx.png', 'com.wenjuanxing.app', '2.1.0', 'https://download.com/wjx', 'https://register.com/wjx', '专业问卷调查平台', 4.2, 89, 3210, '手动', '新人首单奖励翻倍', 20.00, 100.00, 0, 1, 0, 1),
('淘宝联盟', 3, '/logos/tblm.png', 'com.taobao.union', '3.0.1', 'https://download.com/tblm', 'https://register.com/tblm', '淘宝官方返利平台', 4.8, 256, 8900, '手动', '新人专享高佣金', 1.00, 0.00, 1, 1, 1, 1),
('抖音极速版', 4, '/logos/dyjsb.png', 'com.douyin.lite', '1.5.2', 'https://download.com/dyjsb', 'https://register.com/dyjsb', '看视频赚金币', 4.3, 167, 6780, '挂机', '新人7天双倍奖励', 0.30, 10.00, 1, 0, 1, 1),
('趣头条', 5, '/logos/qtt.png', 'com.qutoutiao.news', '2.3.4', 'https://download.com/qtt', 'https://register.com/qtt', '看新闻赚钱', 3.9, 94, 4560, '半自动', '新人阅读奖励加倍', 1.00, 20.00, 0, 0, 0, 1);

-- 4. 创建评测报告表测试数据
INSERT INTO `ct_evaluation_reports` (`app_id`, `user_id`, `title`, `rating`, `app_type`, `run_mode`, `newcomer_benefit`, `withdraw_threshold`, `guarantee_amount`, `test_count`, `total_earnings`, `test_duration`, `test_device`, `evaluator_name`, `evaluation_date`, `earnings_detail`, `report_content`, `audit_status`, `points_awarded`, `view_count`, `like_count`, `status`) VALUES
(1, 1, '开心消消乐深度测评报告', 4.5, '游戏类', '半自动', '新人注册送5元', 10.00, 50.00, 50, 25.80, 120, 'iPhone 13 Pro', '张测评', '2024-01-15', '{"day1": 5.20, "day2": 6.80, "day3": 7.30, "day4": 6.50}', '这款游戏操作简单，收益稳定，新人福利不错，提现门槛合理。经过4天测试，平均每天收益6.45元，总体表现良好。游戏画面精美，操作流畅，适合长期操作。', 1, 50, 156, 23, 1),
(2, 2, '问卷星任务平台测评', 4.0, '任务类', '手动', '新人首单奖励翻倍', 20.00, 100.00, 25, 45.60, 180, 'Xiaomi 12', '李评测', '2024-01-16', '{"survey1": 8.00, "survey2": 12.50, "survey3": 15.10, "survey4": 10.00}', '问卷星平台任务质量较高，单价不错，但需要认真填写。测试期间完成25个问卷，平均单价1.82元。平台审核较严，需要保证答题质量。', 1, 50, 89, 12, 1),
(3, 1, '淘宝联盟返利测评', 4.8, '购物类', '手动', '新人专享高佣金', 1.00, 0.00, 15, 89.30, 60, 'iPhone 13 Pro', '张测评', '2024-01-17', '{"order1": 15.60, "order2": 23.40, "order3": 28.90, "order4": 21.40}', '淘宝联盟作为官方返利平台，佣金率高，提现快速。测试期间通过推广获得89.3元收益，平均每单5.95元。平台稳定可靠，推荐长期使用。', 0, 0, 45, 8, 1),
(4, 3, '抖音极速版挂机测评', 4.2, '视频类', '挂机', '新人7天双倍奖励', 0.30, 10.00, 100, 12.45, 300, 'OPPO Find X5', '王测评', '2024-01-18', '{"hour1": 0.15, "hour2": 0.18, "hour3": 0.12, "hour4": 0.20, "hour5": 0.16}', '抖音极速版挂机收益一般，但操作简单。5小时测试获得12.45元，平均每小时2.49元。新人期间收益翻倍，后期收益会下降。适合空闲时间操作。', 1, 50, 78, 15, 1);

-- 5. 创建评测数据详情表测试数据
INSERT INTO `ct_evaluation_details` (`report_id`, `detail_type`, `title`, `content`, `file_url`, `sort_order`, `extra_data`) VALUES
-- 开心消消乐评测详情
(1, 'screenshot', '游戏主界面截图', '游戏主界面展示，可以看到当前金币数量和任务进度', '/screenshots/xxl_main_1.jpg', 1, '{"width": 1080, "height": 1920, "size": 245678}'),
(1, 'screenshot', 'APP提现记录', '成功提现10元到微信，到账时间约2分钟', '/screenshots/xxl_withdraw_1.jpg', 2, '{"amount": 10.00, "method": "wechat", "time": "2024-01-15 14:30:25"}'),
(1, 'screenshot', '微信到账记录', '微信收到提现金额，确认到账成功', '/screenshots/xxl_wechat_1.jpg', 3, '{"amount": 10.00, "time": "2024-01-15 14:32:18"}'),
(1, 'earning', '收益明细统计', '详细的每日收益统计数据', NULL, 4, '{"total_days": 4, "avg_daily": 6.45, "max_daily": 7.30, "min_daily": 5.20}'),
(1, 'test_data', '测试环境信息', '测试设备和网络环境详情', NULL, 5, '{"device": "iPhone 13 Pro", "os": "iOS 16.2", "network": "WiFi", "location": "北京"}'),

-- 问卷星评测详情
(2, 'screenshot', '问卷列表截图', '可接任务列表，显示单价和预计时间', '/screenshots/wjx_list_1.jpg', 1, '{"task_count": 25, "avg_price": 1.82}'),
(2, 'screenshot', '提现成功截图', '成功提现45.6元到支付宝', '/screenshots/wjx_withdraw_1.jpg', 2, '{"amount": 45.60, "method": "alipay", "time": "2024-01-16 18:45:30"}'),
(2, 'screenshot', '支付宝到账截图', '支付宝收到提现金额', '/screenshots/wjx_alipay_1.jpg', 3, '{"amount": 45.60, "time": "2024-01-16 18:47:12"}'),
(2, 'earning', '任务收益分析', '各类型问卷的收益分析', NULL, 4, '{"survey_types": ["消费调研", "产品体验", "市场调查"], "avg_time": 7.2, "completion_rate": 0.96}'),

-- 淘宝联盟评测详情
(3, 'screenshot', '推广数据截图', '推广订单数据和佣金统计', '/screenshots/tblm_data_1.jpg', 1, '{"order_count": 15, "total_commission": 89.30}'),
(3, 'screenshot', '提现记录截图', '提现89.3元到银行卡', '/screenshots/tblm_withdraw_1.jpg', 2, '{"amount": 89.30, "method": "bank", "time": "2024-01-17 16:20:15"}'),
(3, 'earning', '推广效果分析', '不同商品类目的推广效果对比', NULL, 3, '{"categories": ["数码", "服装", "家居"], "best_category": "数码", "conversion_rate": 0.08}'),

-- 抖音极速版评测详情
(4, 'screenshot', '金币收益截图', '观看视频获得的金币数量', '/screenshots/dyjsb_coins_1.jpg', 1, '{"coins_earned": 12450, "exchange_rate": 1000, "cash_value": 12.45}'),
(4, 'screenshot', '提现到账截图', '微信收到提现金额', '/screenshots/dyjsb_wechat_1.jpg', 2, '{"amount": 12.45, "time": "2024-01-18 20:15:30"}'),
(4, 'test_data', '挂机效果统计', '5小时挂机的详细数据统计', NULL, 3, '{"total_hours": 5, "avg_hourly": 2.49, "video_count": 180, "avg_per_video": 0.069}');

-- 6. 数据验证查询

-- 6.1 验证分类和APP关联
SELECT 
    c.name AS category_name,
    COUNT(a.id) AS app_count,
    AVG(a.rating) AS avg_rating
FROM ct_app_categories c
LEFT JOIN ct_apps a ON c.id = a.category_id AND a.status = 1
WHERE c.status = 1
GROUP BY c.id, c.name
ORDER BY c.sort_order DESC;

-- 6.2 验证APP和评测报告关联
SELECT 
    a.name AS app_name,
    COUNT(r.id) AS report_count,
    AVG(r.rating) AS avg_user_rating,
    SUM(r.view_count) AS total_views
FROM ct_apps a
LEFT JOIN ct_evaluation_reports r ON a.id = r.app_id AND r.status = 1
WHERE a.status = 1
GROUP BY a.id, a.name
ORDER BY report_count DESC;

-- 6.3 验证评测报告和详情关联
SELECT 
    r.title AS report_title,
    COUNT(d.id) AS detail_count,
    GROUP_CONCAT(DISTINCT d.detail_type) AS detail_types
FROM ct_evaluation_reports r
LEFT JOIN ct_evaluation_details d ON r.id = d.report_id
WHERE r.status = 1
GROUP BY r.id, r.title
ORDER BY r.create_time DESC;

-- 6.4 验证审核状态统计
SELECT 
    audit_status,
    CASE 
        WHEN audit_status = 0 THEN '待审核'
        WHEN audit_status = 1 THEN '审核通过'
        WHEN audit_status = 2 THEN '审核拒绝'
        ELSE '未知状态'
    END AS status_name,
    COUNT(*) AS report_count,
    SUM(points_awarded) AS total_points
FROM ct_evaluation_reports
WHERE status = 1
GROUP BY audit_status
ORDER BY audit_status;

-- 6.5 验证数据完整性
-- 检查是否有孤立的APP（没有有效分类）
SELECT a.id, a.name 
FROM ct_apps a 
LEFT JOIN ct_app_categories c ON a.category_id = c.id 
WHERE c.id IS NULL OR c.status = 0;

-- 检查是否有孤立的评测报告（没有有效APP）
SELECT r.id, r.title 
FROM ct_evaluation_reports r 
LEFT JOIN ct_apps a ON r.app_id = a.id 
WHERE a.id IS NULL;

-- 检查是否有孤立的评测详情（没有有效报告）
SELECT d.id, d.title 
FROM ct_evaluation_details d 
LEFT JOIN ct_evaluation_reports r ON d.report_id = r.id 
WHERE r.id IS NULL;

-- 7. 性能测试查询

-- 7.1 测试分类筛选性能
EXPLAIN SELECT * FROM ct_apps 
WHERE category_id = 1 AND status = 1 
ORDER BY is_hot DESC, rating DESC 
LIMIT 10;

-- 7.2 测试评测报告查询性能
EXPLAIN SELECT * FROM ct_evaluation_reports 
WHERE app_id = 1 AND audit_status = 1 
ORDER BY create_time DESC 
LIMIT 10;

-- 7.3 测试用户评测查询性能
EXPLAIN SELECT * FROM ct_evaluation_reports 
WHERE user_id = 1 AND status = 1 
ORDER BY create_time DESC;

-- 7.4 测试复合条件查询性能
EXPLAIN SELECT a.*, AVG(r.rating) as avg_rating, COUNT(r.id) as report_count
FROM ct_apps a
LEFT JOIN ct_evaluation_reports r ON a.id = r.app_id AND r.audit_status = 1
WHERE a.category_id = 1 AND a.status = 1
GROUP BY a.id
HAVING report_count > 0
ORDER BY avg_rating DESC, report_count DESC
LIMIT 20;

-- 8. 清理测试数据（可选）
/*
DELETE FROM ct_evaluation_details WHERE report_id IN (1,2,3,4);
DELETE FROM ct_evaluation_reports WHERE id IN (1,2,3,4);
DELETE FROM ct_apps WHERE id IN (1,2,3,4,5);
DELETE FROM ct_app_categories WHERE id IN (1,2,3,4,5);
*/