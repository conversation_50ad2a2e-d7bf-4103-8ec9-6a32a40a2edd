<template>
	<view class="page-container">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<u-icon name="arrow-left" size="20" color="#333"></u-icon>
			</view>
			<view class="nav-title">提交放水线索</view>
			<view class="nav-right"></view>
		</view>
		
		<!-- 表单内容 -->
		<scroll-view class="content-container" scroll-y>
			<view class="form-container">
				<!-- 选择APP -->
				<view class="form-item">
					<view class="form-label">选择APP</view>
					<view class="select-container" @click="showAppPicker = true">
						<view class="select-input" :class="{ 'placeholder': !formData.selectedApp }">
							{{ formData.selectedApp || '请选择APP' }}
						</view>
						<u-icon name="arrow-down" size="16" color="#999"></u-icon>
					</view>
				</view>
				
				<!-- 放水时间 -->
				<view class="form-item">
					<view class="form-label">放水时间</view>
					<u-datetime-picker 
						v-model="formData.releaseTime"
						mode="datetime"
						:show="showTimePicker"
						@confirm="confirmTime"
						@cancel="showTimePicker = false"
					></u-datetime-picker>
					<view class="select-container" @click="showTimePicker = true">
						<view class="select-input" :class="{ 'placeholder': !formData.releaseTimeText }">
							{{ formData.releaseTimeText || '年/月/日 --:--' }}
						</view>
						<u-icon name="calendar" size="16" color="#999"></u-icon>
					</view>
					<text class="form-tip">默认为当前提交时间</text>
				</view>
				
				<!-- 单包大小 -->
				<view class="form-item">
					<view class="form-label">单包大小(元)</view>
					<view class="radio-row">
						<view 
							v-for="(amount, index) in packageAmounts" 
							:key="index"
							class="radio-item"
							:class="{ 'active': formData.packageAmount === amount }"
							@click="selectPackageAmount(amount)"
						>
							<text>{{ amount }}</text>
						</view>
					</view>
				</view>
				
				<!-- 设备型号 -->
				<view class="form-item">
					<view class="form-label">设备型号</view>
					<u-input 
						v-model="formData.deviceModel" 
						placeholder="如：iPhone 15 Pro / 华为mate40"
						border="surround"
						:clearable="true"
					></u-input>
				</view>
				
				<!-- APP提现记录截图 -->
				<view class="form-item">
					<view class="form-label">APP提现记录截图</view>
					<view class="upload-container" @click="uploadAppScreenshot">
						<view v-if="!appScreenshotUrl" class="upload-placeholder">
							<u-icon name="photo" size="40" color="#999"></u-icon>
							<text class="upload-text">点击上传图片</text>
							<text class="upload-tip">支持JPG、PNG格式</text>
						</view>
						<image v-else :src="appScreenshotUrl" class="uploaded-image" mode="aspectFit"></image>
					</view>
				</view>
				
				<!-- 微信到账记录截图 -->
				<view class="form-item">
					<view class="form-label">微信到账记录截图</view>
					<view class="upload-container" @click="uploadWechatScreenshot">
						<view v-if="!wechatScreenshotUrl" class="upload-placeholder">
							<u-icon name="photo" size="40" color="#999"></u-icon>
							<text class="upload-text">点击上传图片</text>
							<text class="upload-tip">支持JPG、PNG格式</text>
						</view>
						<image v-else :src="wechatScreenshotUrl" class="uploaded-image" mode="aspectFit"></image>
					</view>
				</view>
				
				<!-- 线索描述 -->
				<view class="form-item">
					<view class="form-label">线索描述</view>
					<u-textarea 
						v-model="formData.clueDescription"
						placeholder="详细描述放水情况，包括获得金额、到账时间、注意事项等..."
						:maxlength="300"
						:showWordLimit="true"
						:autoHeight="true"
						border="surround"
					></u-textarea>
				</view>
				
				<!-- 提交按钮 -->
				<view class="submit-section">
					<u-button 
						type="primary"
						size="large"
						:loading="isSubmitting"
						:disabled="isSubmitting"
						@click="handleSubmit"
						shape="round"
						customStyle="background: linear-gradient(135deg, #dc2626, #b91c1c); border: none;"
					>
						{{ isSubmitting ? '提交中...' : '提交线索（+30积分）' }}
					</u-button>
					<text class="submit-tip">提交并审核通过后将获得30积分奖励</text>
				</view>
			</view>
		</scroll-view>
		
		<!-- APP选择器 -->
		<u-picker 
			:show="showAppPicker" 
			:columns="appColumns"
			@confirm="confirmApp"
			@cancel="showAppPicker = false"
		></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				appScreenshotUrl: '',
				wechatScreenshotUrl: '',
				showAppPicker: false,
				showTimePicker: false,
				isSubmitting: false,
				submitTimer: null, // 防抖定时器
				
				formData: {
					selectedApp: '',
					releaseTime: Number(new Date()),
					releaseTimeText: '',
					packageAmount: '',
					deviceModel: '',
					clueDescription: ''
				},
				
				// APP选项
				appColumns: [
					[
						{ text: '金银合合', value: '金银合合' },
						{ text: '趣步多多', value: '趣步多多' },
						{ text: '短剧星球', value: '短剧星球' },
						{ text: '阅读赚', value: '阅读赚' },
						{ text: '游戏红包', value: '游戏红包' },
						{ text: '其他', value: '其他' }
					]
				],
				
				// 单包金额选项
				packageAmounts: ['0.1-0.29', '0.3-0.49', '0.5-0.99', '1以上']
			}
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},
			
			// 确认APP选择
			confirmApp(e) {
				this.formData.selectedApp = e.value[0]
				this.showAppPicker = false
			},
			
			// 确认时间选择
			confirmTime(e) {
				this.formData.releaseTime = e.value
				this.formData.releaseTimeText = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
				this.showTimePicker = false
			},
			
			// 选择单包金额
			selectPackageAmount(amount) {
				this.formData.packageAmount = amount
			},
			
			// 上传APP提现记录截图
			uploadAppScreenshot() {
				this.chooseAndUploadImage('app')
			},
			
			// 上传微信截图
			uploadWechatScreenshot() {
				this.chooseAndUploadImage('wechat')
			},
			
			// 选择并上传图片
			chooseAndUploadImage(type) {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['camera', 'album'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0]
						
						// 这里应该调用实际的上传接口
						// 暂时使用临时路径模拟
						switch(type) {
							case 'app':
								this.appScreenshotUrl = tempFilePath
								break
							case 'wechat':
								this.wechatScreenshotUrl = tempFilePath
								break
						}
						
						uni.showToast({
							title: '上传成功',
							icon: 'success'
						})
					},
					fail: (err) => {
						uni.showToast({
							title: '上传失败',
							icon: 'error'
						})
					}
				})
			},
			
			// 表单验证
			validateForm() {
				if (!this.formData.selectedApp) {
					uni.showToast({ title: '请选择APP', icon: 'none' })
					return false
				}
				
				if (!this.formData.packageAmount) {
					uni.showToast({ title: '请选择单包大小', icon: 'none' })
					return false
				}
				
				if (!this.formData.deviceModel) {
					uni.showToast({ title: '请输入设备型号', icon: 'none' })
					return false
				}
				
				if (!this.formData.clueDescription) {
					uni.showToast({ title: '请填写线索描述', icon: 'none' })
					return false
				}
				
				if (!this.appScreenshotUrl) {
					uni.showToast({ title: '请上传APP提现记录截图', icon: 'none' })
					return false
				}
				
				if (!this.wechatScreenshotUrl) {
					uni.showToast({ title: '请上传微信到账记录截图', icon: 'none' })
					return false
				}
				
				return true
			},
			
			// 提交表单（带防抖）
			handleSubmit() {
				// 防抖处理
				if (this.submitTimer) {
					clearTimeout(this.submitTimer)
				}
				
				this.submitTimer = setTimeout(() => {
					this.submitForm()
				}, 300)
			},
			
			// 实际提交表单
			async submitForm() {
				if (this.isSubmitting) return
				
				if (!this.validateForm()) return
				
				this.isSubmitting = true
				
				try {
					// 这里应该调用实际的提交接口
					await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟网络请求
					
					uni.showToast({
						title: '提交成功',
						icon: 'success',
						duration: 2000
					})
					
					// 提交成功后返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
					
				} catch (error) {
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'error'
					})
				} finally {
					this.isSubmitting = false
				}
			}
		},
		
		onLoad() {
			// 初始化时间为当前时间
			this.formData.releaseTimeText = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM')
		},
		
		onUnload() {
			// 清理定时器
			if (this.submitTimer) {
				clearTimeout(this.submitTimer)
			}
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	height: 88rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
	
	.nav-left, .nav-right {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
}

.content-container {
	padding-top: 88rpx;
	height: calc(100vh - 88rpx);
}

.form-container {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 40rpx;
	
	.form-label {
		font-size: 28rpx;
		font-weight: 500;
		color: #374151;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.form-tip {
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 8rpx;
		display: block;
	}
}

.select-container {
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	background-color: #ffffff;
	padding: 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.2s ease;
	
	.select-input {
		flex: 1;
		font-size: 28rpx;
		color: #111827;
		
		&.placeholder {
			color: #9ca3af;
		}
	}
}

.select-container:active {
	background-color: #f9fafb;
	border-color: #9ca3af;
}

.upload-container {
	border: 2rpx dashed #d1d5db;
	border-radius: 16rpx;
	background-color: #ffffff;
	min-height: 240rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	cursor: pointer !important;
	transition: all 0.2s ease;
	
	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
		
		.upload-text {
			font-size: 28rpx;
			color: #6b7280;
			margin-top: 16rpx;
		}
		
		.upload-tip {
			font-size: 24rpx;
			color: #9ca3af;
			margin-top: 8rpx;
		}
	}
	
	.uploaded-image {
		width: 100%;
		height: 240rpx;
		border-radius: 12rpx;
	}
}

.upload-container:hover {
	background-color: #f9fafb !important;
	border-color: #9ca3af !important;
}

.upload-container:active {
	background-color: #f3f4f6 !important;
	border-color: #9ca3af !important;
	transform: scale(0.98);
}

.radio-row {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.radio-item {
	flex: 1;
	min-width: 160rpx;
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	padding: 24rpx 16rpx;
	text-align: center;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	
	text {
		font-size: 28rpx;
		color: #374151;
	}
	
	&.active {
		border-color: #dc2626;
		background-color: #fef2f2;
		
		text {
			color: #dc2626;
			font-weight: 500;
		}
	}
	
	&:active {
		transform: scale(0.98);
	}
}

.submit-section {
	margin-top: 60rpx;
	margin-bottom: 60rpx;
	
	.submit-tip {
		display: block;
		text-align: center;
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 16rpx;
	}
}

/* uview组件样式覆盖 */
:deep(.u-input__content) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-textarea) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-button) {
	height: 96rpx !important;
	font-size: 32rpx !important;
	font-weight: 600 !important;
}
</style> 