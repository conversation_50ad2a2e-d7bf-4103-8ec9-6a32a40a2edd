[版本日志]
日期：2025-01-27
修改内容：

## 1. 优化 UserService::pushWithdrawDataToThird 方法的异常处理机制

**修改文件：** `app/Service/User/UserService.php`
**修改位置：** 第2291行开始的 `pushWithdrawDataToThird()` 方法

### 问题描述
原方法在处理HTTP错误响应时存在以下问题：
- 当第三方API返回500错误时，Guzzle会抛出包含完整错误信息的异常
- 异常消息格式为：`Server error: POST https://xxx resulted in a 500 Internal Server Error response: {"status":500,"code":500,"message":"请联系客服进行该操作"}`
- 需要通过复杂的正则表达式从异常消息中提取JSON中的message字段

### 根本原因
Guzzle HTTP客户端默认设置 `http_errors => true`，会为4xx和5xx响应自动抛出异常，将完整错误信息包装在异常消息中，而不是直接返回响应体。

### 解决方案

1. **禁用Guzzle自动异常抛出**
   ```php
   $client = new Client([
       'verify' => false,
       'timeout' => 10,
       'headers' => [
           'Content-Type' => 'application/json',
           'Accept' => 'application/json'
       ],
       // 关键修改：禁止为4xx和5xx响应抛出异常
       'http_errors' => false
   ]);
   ```

2. **手动处理HTTP状态码**
   ```php
   $statusCode = $response->getStatusCode();
   $responseBody = $response->getBody()->getContents();
   $result = json_decode($responseBody, true);

   // 检查HTTP状态码
   if ($statusCode >= 400) {
       // 直接从JSON响应中提取错误信息
       $errorMessage = '操作失败';
       if ($result && isset($result['message'])) {
           $errorMessage = $result['message'];
       } elseif ($result && isset($result['msg'])) {
           $errorMessage = $result['msg'];
       }
       throw new MyException($errorMessage);
   }
   ```

3. **优化异常处理逻辑**
   ```php
   } catch (\Exception $e) {
       // 如果是我们自己抛出的MyException，直接重新抛出
       if ($e instanceof MyException) {
           throw $e;
       }
       
       // 处理其他异常（如网络异常、JSON解析异常等）
       throw new MyException('网络请求失败：' . $e->getMessage());
   }
   ```

### 修改效果

**修改前：**
- 需要使用复杂正则表达式：`/"message":"((?:[^"\\]|\\.)*)"/`
- 需要处理转义字符：`str_replace('\"', '"', $messageContent)`
- 代码复杂，容易出错

**修改后：**
- 直接从JSON响应中获取错误信息：`$result['message']`
- 代码简洁清晰，易于维护
- 能够正确处理各种HTTP错误状态码

### 技术要点

1. **Guzzle配置优化**
   - 设置 `'http_errors' => false` 禁用自动异常抛出
   - 手动检查HTTP状态码进行错误处理

2. **错误信息提取优化**
   - 优先提取 `message` 字段
   - 其次提取 `msg` 字段作为备选
   - 提供默认错误信息作为兜底

3. **异常分类处理**
   - 区分自定义异常（MyException）和系统异常
   - 对不同类型异常提供不同的处理策略

### 影响范围

- **功能影响：** 提现到第三方平台的错误处理体验优化
- **代码维护：** 简化异常处理逻辑，提高代码可读性
- **用户体验：** 能够准确显示第三方API返回的具体错误信息

### 测试建议

1. 测试正常的第三方提现流程
2. 模拟各种HTTP错误状态码（400、401、500等）
3. 验证错误信息是否能正确提取和显示
4. 测试网络异常等其他异常情况的处理 