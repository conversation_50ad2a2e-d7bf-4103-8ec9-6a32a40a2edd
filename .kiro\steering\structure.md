# Project Structure

## Root Level Files
- **App.vue**: Main application component with global styles and lifecycle hooks
- **main.js**: Application entry point with Vue initialization and plugin setup
- **pages.json**: Page routing configuration and tab bar setup
- **manifest.json**: uni-app configuration for different platforms
- **env.js**: Environment variables and API configuration
- **uni.scss**: Global SCSS variables and mixins

## Directory Organization

### `/pages/` - Application Pages
Each page follows the pattern: `pages/{page-name}/{page-name}.vue`
- **index/**: Home page with search and content discovery
- **evaluation/**: App/task evaluation functionality
- **clue/**: Clue browsing and management
- **invite/**: User invitation system
- **profile/**: User profile and account settings
- **login/**: Authentication page
- **detail/**: Content detail views
- **submit-report/**: Report submission forms
- **submit-clue/**: Clue submission forms

### `/config/` - Configuration Files
- **request.js**: HTTP client configuration with interceptors and error handling

### `/utils/` - Utility Functions
- **storage.js**: Token management and local storage utilities

### `/static/` - Static Assets
- **tabbar/**: Tab bar icons (selected/unselected states)

### `/components/` - Reusable Components
Currently empty - custom components should be placed here

### `/common/` - Shared Resources
- **utils/**: Additional utility functions (currently closed)

### `/unpackage/` - Build Output
- **dist/**: Compiled application files
- **res/**: Generated resources and icons

## File Naming Conventions
- **Pages**: Use kebab-case for page directories and files
- **Components**: Follow Vue component naming conventions
- **Assets**: Use descriptive names with platform/state suffixes
- **Utilities**: Use camelCase for function names

## Code Organization Patterns
- **Single File Components**: Each page is a complete `.vue` file with template, script, and style
- **Centralized Configuration**: API and environment settings in dedicated config files
- **Utility Separation**: Helper functions separated by functionality
- **Asset Organization**: Platform-specific assets grouped by usage