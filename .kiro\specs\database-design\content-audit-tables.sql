-- =============================================
-- 内容审核系统表结构设计
-- 创建时间: 2025-01-08
-- 描述: 支持次推应用的内容审核、规则管理和审核日志功能
-- =============================================

-- 1. 审核规则表 (ct_audit_rules)
-- 用途: 管理不同类型内容的审核规则和流程配置
CREATE TABLE ct_audit_rules (
    rule_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规则代码',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型(evaluation_report,water_clue,user_profile等)',
    rule_description TEXT COMMENT '规则描述',
    auto_audit_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用自动审核(0:否 1:是)',
    auto_audit_keywords TEXT COMMENT '自动审核关键词(JSON格式)',
    manual_audit_required TINYINT(1) DEFAULT 1 COMMENT '是否需要人工审核(0:否 1:是)',
    audit_timeout_hours INT DEFAULT 72 COMMENT '审核超时时间(小时)',
    pass_score_threshold INT DEFAULT 80 COMMENT '自动通过分数阈值',
    reject_score_threshold INT DEFAULT 20 COMMENT '自动拒绝分数阈值',
    reward_points INT DEFAULT 0 COMMENT '审核通过奖励积分',
    penalty_points INT DEFAULT 0 COMMENT '审核不通过扣除积分',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_by BIGINT DEFAULT NULL COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_rule_code (rule_code),
    INDEX idx_content_type (content_type),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES ct_admin_users(admin_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审核规则表';

-- 2. 内容审核表 (ct_content_audits)
-- 用途: 记录所有需要审核的内容及其审核状态
CREATE TABLE ct_content_audits (
    audit_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '审核ID',
    rule_id INT NOT NULL COMMENT '审核规则ID',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型',
    content_id BIGINT NOT NULL COMMENT '内容对象ID',
    submitter_id BIGINT NOT NULL COMMENT '提交者用户ID',
    audit_status ENUM('pending', 'reviewing', 'passed', 'rejected', 'timeout') DEFAULT 'pending' COMMENT '审核状态',
    auto_audit_score INT DEFAULT NULL COMMENT '自动审核分数',
    auto_audit_result ENUM('pass', 'reject', 'manual') DEFAULT NULL COMMENT '自动审核结果',
    auto_audit_reason TEXT COMMENT '自动审核原因',
    manual_audit_required TINYINT(1) DEFAULT 1 COMMENT '是否需要人工审核',
    auditor_id BIGINT DEFAULT NULL COMMENT '审核员ID',
    audit_result ENUM('pass', 'reject') DEFAULT NULL COMMENT '最终审核结果',
    audit_reason TEXT COMMENT '审核原因/备注',
    audit_score INT DEFAULT NULL COMMENT '人工审核分数',
    reject_category VARCHAR(50) DEFAULT NULL COMMENT '拒绝分类(spam,inappropriate,fake等)',
    content_snapshot JSON COMMENT '内容快照(审核时的内容状态)',
    priority_level ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交审核时间',
    audit_started_at TIMESTAMP NULL DEFAULT NULL COMMENT '开始审核时间',
    audit_completed_at TIMESTAMP NULL DEFAULT NULL COMMENT '审核完成时间',
    timeout_at TIMESTAMP NULL DEFAULT NULL COMMENT '审核超时时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_rule_id (rule_id),
    INDEX idx_content_type (content_type),
    INDEX idx_content_id (content_id),
    INDEX idx_submitter_id (submitter_id),
    INDEX idx_auditor_id (auditor_id),
    INDEX idx_audit_status (audit_status),
    INDEX idx_audit_result (audit_result),
    INDEX idx_priority_level (priority_level),
    INDEX idx_submitted_at (submitted_at),
    INDEX idx_audit_completed_at (audit_completed_at),
    INDEX idx_timeout_at (timeout_at),
    
    -- 复合索引
    INDEX idx_content_type_id (content_type, content_id),
    INDEX idx_status_priority (audit_status, priority_level),
    INDEX idx_auditor_status (auditor_id, audit_status),
    INDEX idx_submitter_status (submitter_id, audit_status),
    
    -- 唯一约束
    UNIQUE KEY uk_content_audit (content_type, content_id),
    
    -- 外键约束
    FOREIGN KEY (rule_id) REFERENCES ct_audit_rules(rule_id) ON DELETE RESTRICT,
    FOREIGN KEY (submitter_id) REFERENCES ct_users(user_id) ON DELETE RESTRICT,
    FOREIGN KEY (auditor_id) REFERENCES ct_admin_users(admin_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容审核表';

-- 3. 审核日志表 (ct_audit_logs)
-- 用途: 记录审核过程中的所有操作和状态变更
CREATE TABLE ct_audit_logs (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    audit_id BIGINT NOT NULL COMMENT '审核ID',
    operator_id BIGINT DEFAULT NULL COMMENT '操作人ID',
    operator_type ENUM('system', 'admin', 'auto') DEFAULT 'system' COMMENT '操作人类型',
    action_type VARCHAR(50) NOT NULL COMMENT '操作类型(submit,assign,review,approve,reject,timeout等)',
    old_status VARCHAR(20) DEFAULT NULL COMMENT '原状态',
    new_status VARCHAR(20) NOT NULL COMMENT '新状态',
    action_reason TEXT COMMENT '操作原因',
    action_data JSON COMMENT '操作相关数据',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '操作IP地址',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    processing_time INT DEFAULT NULL COMMENT '处理耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引设计
    INDEX idx_audit_id (audit_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_operator_type (operator_type),
    INDEX idx_action_type (action_type),
    INDEX idx_old_status (old_status),
    INDEX idx_new_status (new_status),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_audit_action (audit_id, action_type),
    INDEX idx_operator_action (operator_id, action_type),
    INDEX idx_status_change (old_status, new_status),
    
    -- 外键约束
    FOREIGN KEY (audit_id) REFERENCES ct_content_audits(audit_id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES ct_admin_users(admin_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审核日志表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入默认审核规则
INSERT INTO ct_audit_rules (rule_name, rule_code, content_type, rule_description, auto_audit_enabled, manual_audit_required, audit_timeout_hours, reward_points, penalty_points, sort_order) VALUES
('评测报告审核', 'evaluation_report_audit', 'evaluation_report', '评测报告内容审核规则', 1, 1, 48, 10, 5, 1),
('放水线索审核', 'water_clue_audit', 'water_clue', '放水线索内容审核规则', 1, 1, 24, 5, 3, 2),
('用户资料审核', 'user_profile_audit', 'user_profile', '用户资料信息审核规则', 0, 1, 72, 0, 0, 3),
('用户头像审核', 'user_avatar_audit', 'user_avatar', '用户头像图片审核规则', 1, 0, 12, 0, 0, 4),
('APP图标审核', 'app_logo_audit', 'app_logo', 'APP图标审核规则', 1, 0, 6, 0, 0, 5);

-- =============================================
-- 触发器设计
-- =============================================

-- 审核状态变更触发器：自动记录状态变更日志
DELIMITER $$
CREATE TRIGGER tr_audit_status_change
AFTER UPDATE ON ct_content_audits
FOR EACH ROW
BEGIN
    -- 如果状态发生变化，记录日志
    IF OLD.audit_status != NEW.audit_status THEN
        INSERT INTO ct_audit_logs (
            audit_id, 
            operator_id, 
            operator_type, 
            action_type, 
            old_status, 
            new_status, 
            action_reason
        ) VALUES (
            NEW.audit_id,
            NEW.auditor_id,
            CASE 
                WHEN NEW.auditor_id IS NULL THEN 'system'
                ELSE 'admin'
            END,
            CASE NEW.audit_status
                WHEN 'reviewing' THEN 'start_review'
                WHEN 'passed' THEN 'approve'
                WHEN 'rejected' THEN 'reject'
                WHEN 'timeout' THEN 'timeout'
                ELSE 'status_change'
            END,
            OLD.audit_status,
            NEW.audit_status,
            NEW.audit_reason
        );
    END IF;
    
    -- 设置审核时间戳
    IF OLD.audit_status = 'pending' AND NEW.audit_status = 'reviewing' THEN
        SET NEW.audit_started_at = CURRENT_TIMESTAMP;
    END IF;
    
    IF OLD.audit_status IN ('pending', 'reviewing') AND NEW.audit_status IN ('passed', 'rejected') THEN
        SET NEW.audit_completed_at = CURRENT_TIMESTAMP;
    END IF;
END$$
DELIMITER ;

-- 审核超时检查触发器：设置超时时间
DELIMITER $$
CREATE TRIGGER tr_audit_timeout_setup
BEFORE INSERT ON ct_content_audits
FOR EACH ROW
BEGIN
    DECLARE timeout_hours INT DEFAULT 72;
    
    -- 获取规则的超时时间
    SELECT audit_timeout_hours INTO timeout_hours 
    FROM ct_audit_rules 
    WHERE rule_id = NEW.rule_id;
    
    -- 设置超时时间
    SET NEW.timeout_at = DATE_ADD(NEW.submitted_at, INTERVAL timeout_hours HOUR);
END$$
DELIMITER ;

-- 审核提交日志触发器：记录提交日志
DELIMITER $$
CREATE TRIGGER tr_audit_submit_log
AFTER INSERT ON ct_content_audits
FOR EACH ROW
BEGIN
    INSERT INTO ct_audit_logs (
        audit_id, 
        operator_id, 
        operator_type, 
        action_type, 
        old_status, 
        new_status, 
        action_reason
    ) VALUES (
        NEW.audit_id,
        NEW.submitter_id,
        'system',
        'submit',
        NULL,
        NEW.audit_status,
        '内容提交审核'
    );
END$$
DELIMITER ;