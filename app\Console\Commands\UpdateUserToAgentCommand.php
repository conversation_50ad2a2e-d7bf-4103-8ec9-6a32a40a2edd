<?php

namespace App\Console\Commands;

use App\Models\User\User;
use App\Models\User\AdminUser;
use App\Models\User\UserRelation;
use App\Utils\Tools;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateUserToAgentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:update-to-agent';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '批量检查并将普通用户升级为代理用户';

    /**
     * 每批处理的数量
     */
    protected $batchSize = 100;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始处理用户升级代理任务...');
        
        try {
            $lastId = 0;
            $processing = true;

            while ($processing) {
                // 每次获取大于上一次最大id的100条记录
                $users = User::query()
                    ->whereNotExists(function ($query) {
                        $query->select(DB::raw(1))
                              ->from('user_relation')
                              ->whereColumn('user_relation.user_id', 'user.id');
                    })
                    ->where('id', '>', $lastId)
                    ->orderBy('id')
                    ->limit($this->batchSize)
                    ->get();

                // 如果没有数据了,结束循环
                if ($users->isEmpty()) {
                    $processing = false;
                    continue;
                }

                foreach ($users as $user) {
                    $this->processUser($user);
                    $lastId = $user->id; // 更新最后处理的ID
                }

                // 释放内存
                unset($users);
            }

            $this->info('用户升级代理任务处理完成');
            return 0;
        } catch (\Exception $e) {
            $this->error('处理过程中出现错误: ' . $e->getMessage());
            Log::error('用户升级代理任务失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 处理单个用户
     */
    protected function processUser($user)
    {
        $this->info("正在处理用户ID: {$user->id}");
        
        try {
            DB::beginTransaction();
            
            // 1. 检查并生成invite_code
            if (empty($user->invite_code)) {
                $user->invite_code = $this->generateInviteCode();
                $user->save();
            }
            
            // 2. 检查是否已有代理账号
            $existingAdmin = AdminUser::where('user_id', $user->id)->first();
            if (!$existingAdmin) {
                // 创建代理账号
                $adminData = [
                    'user_id' => $user->id,
                    'username' => $user->phone,
                    'phone' => $user->phone,
                    'password' => Tools::agentPwd('app112233'),
                    'real_name' => 'app_' . $user->id,
                    'status' => 1,
                    'type' => 2,
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                $admin = AdminUser::create($adminData);
                
                // 更新user表的admin_id
                $user->admin_id = $admin->id;
                $user->save();
            }
            
            // 3. 记录处理状态到user_relation表
            $adminId = $existingAdmin ? $existingAdmin->id : $admin->id;
            
            // 检查是否已存在记录
            $existingRelation = UserRelation::where('user_id', $user->id)
                ->where('admin_id', $adminId)
                ->first();
                
            if (!$existingRelation) {
                UserRelation::create([
                    'user_id' => $user->id,
                    'admin_id' => $adminId,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            }

            $user->identity = 2;
            $user->save();

            DB::commit();
            $this->info("用户ID {$user->id} 处理完成");
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("处理用户ID {$user->id} 失败: " . $e->getMessage());
            Log::error("处理用户升级代理失败", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 生成唯一的6位注册码
     */
    protected function generateInviteCode()
    {
        $maxAttempts = 30;
        $attempts = 0;
        
        do {
            $code = Tools::generateRandomStr(6);
            $exists = User::where('invite_code', $code)->exists();
            $attempts++;
            
            if (!$exists) {
                return $code;
            }
        } while ($attempts < $maxAttempts);
        
        throw new \Exception('无法生成唯一邀请码,请重试');
    }
} 