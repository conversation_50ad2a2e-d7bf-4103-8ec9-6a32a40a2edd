# 用户管理表结构关系图

## 实体关系图 (ERD)

```mermaid
erDiagram
    ct_users {
        bigint id PK "用户ID"
        varchar phone UK "手机号"
        varchar password "登录密码"
        varchar nick_name "用户昵称"
        varchar avatar "用户头像URL"
        varchar real_name "真实姓名"
        varchar id_card "身份证号"
        tinyint gender "性别"
        date birthday "生日"
        varchar email "邮箱地址"
        varchar wechat_openid "微信OpenID"
        varchar wechat_unionid "微信UnionID"
        tinyint status "用户状态"
        tinyint is_verified "是否实名认证"
        int total_points "用户总积分"
        int available_points "可用积分"
        varchar invite_code UK "用户邀请码"
        bigint referrer_id FK "推荐人用户ID"
        tinyint referrer_level "推荐层级"
        datetime last_login_time "最后登录时间"
        varchar last_login_ip "最后登录IP"
        int login_count "登录次数统计"
        varchar register_ip "注册IP地址"
        varchar register_device "注册设备信息"
        tinyint is_deleted "是否删除"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        datetime deleted_at "删除时间"
    }

    ct_user_logins {
        bigint id PK "记录ID"
        bigint user_id FK "用户ID"
        tinyint login_type "登录类型"
        varchar login_ip "登录IP地址"
        varchar login_location "登录地理位置"
        varchar device_type "设备类型"
        varchar device_model "设备型号"
        varchar device_id "设备唯一标识"
        varchar os_version "操作系统版本"
        varchar app_version "APP版本号"
        text user_agent "用户代理信息"
        varchar network_type "网络类型"
        varchar screen_resolution "屏幕分辨率"
        tinyint login_status "登录状态"
        varchar failure_reason "登录失败原因"
        varchar session_id "会话ID"
        datetime logout_time "退出登录时间"
        int session_duration "会话持续时间"
        datetime created_at "登录时间"
    }

    ct_user_relations {
        bigint id PK "关系ID"
        bigint user_id FK "用户ID"
        bigint referrer_id FK "推荐人用户ID"
        tinyint relation_type "关系类型"
        tinyint relation_level "推荐层级"
        datetime bind_time "绑定时间"
        varchar bind_ip "绑定IP地址"
        varchar bind_device "绑定设备信息"
        varchar invite_code_used "使用的邀请码"
        int reward_points "推荐奖励积分"
        tinyint is_active "关系是否有效"
        tinyint status "状态"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    ct_admin_users {
        bigint id PK "管理员ID"
        varchar username UK "管理员用户名"
        varchar password "登录密码"
        varchar real_name "管理员真实姓名"
        varchar email "邮箱地址"
        varchar phone "手机号"
        varchar avatar "头像URL"
        tinyint role_type "角色类型"
        text permissions "权限列表"
        varchar department "所属部门"
        varchar position "职位"
        tinyint status "状态"
        datetime last_login_time "最后登录时间"
        varchar last_login_ip "最后登录IP"
        int login_count "登录次数"
        datetime password_updated_at "密码最后更新时间"
        tinyint is_super_admin "是否超级管理员"
        bigint created_by FK "创建人ID"
        tinyint is_deleted "是否删除"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        datetime deleted_at "删除时间"
    }

    %% 定义关系
    ct_users ||--o{ ct_user_logins : "用户登录记录"
    ct_users ||--o{ ct_user_relations : "被推荐用户"
    ct_users ||--o{ ct_user_relations : "推荐人"
    ct_users ||--o| ct_users : "推荐关系"
    ct_admin_users ||--o{ ct_admin_users : "创建关系"
```

## 表关系说明

### 1. 用户自关联关系
- **ct_users.referrer_id → ct_users.id**
- 用途：建立用户推荐关系
- 关系类型：一对多（一个用户可以推荐多个用户）
- 约束：外键约束，支持NULL值

### 2. 用户登录记录关系
- **ct_user_logins.user_id → ct_users.id**
- 用途：记录用户登录历史
- 关系类型：一对多（一个用户有多条登录记录）
- 约束：外键约束，级联删除

### 3. 用户关系管理
- **ct_user_relations.user_id → ct_users.id**
- **ct_user_relations.referrer_id → ct_users.id**
- 用途：详细管理用户推荐关系
- 关系类型：多对多（通过中间表实现）
- 约束：外键约束，级联删除，唯一约束防重复

### 4. 管理员自关联关系
- **ct_admin_users.created_by → ct_admin_users.id**
- 用途：记录管理员创建关系
- 关系类型：一对多（一个管理员可以创建多个管理员）
- 约束：外键约束，支持NULL值

## 索引策略

### 主键索引
- 所有表的 `id` 字段自动创建主键索引
- 保证记录唯一性和查询性能

### 唯一索引
- `ct_users.phone`: 防止手机号重复注册
- `ct_users.invite_code`: 保证邀请码唯一性
- `ct_user_relations(user_id, referrer_id)`: 防止重复推荐关系
- `ct_admin_users.username`: 保证管理员用户名唯一

### 外键索引
- 所有外键字段自动创建索引
- 优化关联查询性能

### 业务索引
- 状态字段索引：优化状态筛选查询
- 时间字段索引：优化时间范围查询
- 设备字段索引：支持设备管理功能

## 数据完整性保证

### 1. 引用完整性
- 外键约束确保关联数据存在
- 级联删除保持数据一致性
- NULL值处理避免孤立数据

### 2. 域完整性
- 字段类型约束确保数据格式正确
- 默认值设置避免空值问题
- 长度限制防止数据溢出

### 3. 实体完整性
- 主键约束保证记录唯一性
- 唯一约束防止业务数据重复
- 非空约束确保关键字段有值

### 4. 用户定义完整性
- 状态字段值范围检查
- 业务规则通过应用层控制
- 触发器实现复杂业务约束