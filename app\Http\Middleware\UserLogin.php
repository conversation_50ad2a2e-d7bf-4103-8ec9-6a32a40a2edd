<?php
declare(strict_types=1);

namespace App\Http\Middleware;
use App\Exceptions\MyException;
use App\Service\User\Auth\LoginService;
use Closure;
use Illuminate\Http\Request;
class UserLogin
{
    protected LoginService $loginService;
    public function __construct(LoginService $loginService)
    {
        $this->loginService = $loginService;
    }
    public function handle(Request $request, Closure $next)
    {
        $authResult = $this->loginService->loginAuth();
        $request->attributes->set('user', $authResult['user']);
        $request->attributes->set('token', $authResult['token']); 
        return $next($request);
    }
}
