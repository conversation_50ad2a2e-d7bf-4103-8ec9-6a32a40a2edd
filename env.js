// 环境配置
export const DOMAIN = (() => {
    // 使用process.env.NODE_ENV判断环境
    const isProd = process.env.NODE_ENV === 'production';
    // 生产环境
    if (isProd) {
      // 增加容错处理
      try {
        // 判断window是否可用
        if (typeof window !== 'undefined' && window.location) {
          return window.location.origin;
        }
      } catch (e) {
        console.warn('获取window.location失败');
      }
    }
    
    // 开发环境或获取失败时的默认值
	//return 'https://mt.yunshumai.com';
	return 'http://citui.test.com';
  })();
export const PROJECT_NAME     = '次推';
export const API_DOMAIN = `${DOMAIN}`;
export const IMG_DOMAIN= `${DOMAIN}`;
export const API_BASE= '/api';
export const IMG_BASE= '/api';
export const UPLOAD_API='/user/upload/image'
export const ENVMODEL     = 'production';