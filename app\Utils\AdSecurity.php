<?php
namespace App\Utils;

class AdSecurity
{
    /**
     * 加密方法
     *
     * @param array $data 要加密的数据
     * @param string $appKey 加密密钥
     * @return array 包含加密后的数据和签名
     */
    public static function encrypt(array $data, string $appKey): array
    {
        // 添加时间戳，防止重放攻击
        $data['timestamp'] = time();

        // 对数据进行排序
        ksort($data);

        // 生成签名
        $sign = self::generateSign($data, $appKey);

        // 将数据转为JSON
        $jsonData = json_encode($data);

        // 使用AES加密数据
        $encryptedData = self::aesEncrypt($jsonData, $appKey);

        return [
            'encrypted_data' => base64_encode($encryptedData),
            'sign' => $sign
        ];
    }

    /**
     * 兼容旧版本的加密方法
     *
     * @param array $data 要加密的数据
     * @param string $appKey 加密密钥
     * @return array 包含加密后的数据和签名
     */
    public static function encryptLegacy(array $data, string $appKey): array
    {
        // 添加时间戳，防止重放攻击
        $data['timestamp'] = time();

        // 对数据进行排序
        ksort($data);

        // 生成签名
        $sign = self::generateSign($data, $appKey);

        // 将数据转为JSON
        $jsonData = json_encode($data);

        // 使用AES加密数据
        $encryptedData = self::aesEncrypt($jsonData, $appKey);

        return [
            'data' => base64_encode($encryptedData),
            'sign' => $sign
        ];
    }

    /**
     * 解密方法
     *
     * @param string $encryptedData 加密后的数据
     * @param string $sign 签名
     * @param string $appKey 加密密钥
     * @param int $expireTime 过期时间（秒）
     * @return array|false 解密后的数据，失败返回false
     */
    public static function decrypt(string $encryptedData, string $sign, string $appKey, int $expireTime = 60): array|false
    {
        // Base64解码
        $encryptedData = base64_decode($encryptedData);

        // AES解密
        $jsonData = self::aesDecrypt($encryptedData, $appKey);
        if ($jsonData === false) {
            return false;
        }

        // JSON解码
        $data = json_decode($jsonData, true);
        if (!is_array($data)) {
            return false;
        }

        // 验证时间戳
        if (!isset($data['timestamp']) || (time() - $data['timestamp']) > $expireTime) {
            return false;
        }

        // 验证签名
        $calculatedSign = self::generateSign($data, $appKey);
        if ($calculatedSign !== $sign) {
            return false;
        }

        return $data;
    }

    /**
     * 解密前端加密的数据
     *
     * @param array $params 包含 encrypted_data 和 sign 的数组
     * @param string $appKey 加密密钥
     * @param int $expireTime 过期时间（秒）
     * @return array|false 解密后的数据，失败返回false
     */
    public static function decryptFromClient(array $params, string $appKey, int $expireTime = 3600): array|false
    {
        // 检查必要参数
        if (!isset($params['encrypted_data']) || !isset($params['sign'])) {
            return false;
        }

        $encryptedData = $params['encrypted_data'];
        $sign = $params['sign'];

        // Base64解码
        $encryptedData = base64_decode($encryptedData);

        // AES解密
        $jsonData = self::aesDecrypt($encryptedData, $appKey);

        if ($jsonData === false) {
            return false;
        }

        // JSON解码
        $data = json_decode($jsonData, true);
        if (!is_array($data)) {
            return false;
        }

        // 验证时间戳
        if (!isset($data['timestamp']) || (time() - $data['timestamp']) > $expireTime) {
            return false;
        }
        // 验证签名
        $calculatedSign = self::generateSign($data, $appKey);
        if ($calculatedSign !== $sign) {
            return false;
        }

        return $data;
    }

    /**
     * 生成签名
     *
     * @param array $data 数据
     * @param string $appKey 密钥
     * @return string 签名
     */
    private static function generateSign(array $data, string $appKey): string
    {
        // 对数据进行排序
        ksort($data);
        // 自定义构建查询字符串，确保与前端编码一致
        $parts = [];
        foreach ($data as $key => $value) {
            // 使用 rawurlencode 确保与 JavaScript encodeURIComponent 行为一致
            // 将所有值转换为字符串，避免整数类型错误
            $encodedKey = rawurlencode((string)$key);
            $encodedValue = rawurlencode((string)$value);
            $parts[] = $encodedKey . '=' . $encodedValue;
        }
        $queryString = implode('&', $parts);
        // 使用HMAC-SHA256生成签名
        return hash_hmac('sha256', $queryString, $appKey);
    }

    /**
     * AES加密
     *
     * @param string $data 要加密的数据
     * @param string $key 密钥
     * @return string 加密后的数据
     */
    private static function aesEncrypt(string $data, string $key): string
    {
        $key = substr(hash('sha256', $key, true), 0, 32);
        $iv = random_bytes(16); // 生成随机IV

        $encrypted = openssl_encrypt(
            $data,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        // 将IV和加密后的数据拼接在一起
        return $iv . $encrypted;
    }

    /**
     * AES解密
     *
     * @param string $data 加密后的数据
     * @param string $key 密钥
     * @return string|false 解密后的数据，失败返回false
     */
    private static function aesDecrypt(string $data, string $key): string|false
    {
        // 使用SHA-256哈希密钥，取前32字节
        $key = substr(hash('sha256', $key, true), 0, 32);

        // 提取IV（前16字节）
        if (strlen($data) < 16) {
            return false; // 数据长度不足，无法提取IV
        }

        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        // 尝试解密
        $decrypted = openssl_decrypt(
            $encrypted,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        // 如果解密失败，记录错误信息
        if ($decrypted === false) {
            $error = openssl_error_string();
            error_log("AES解密失败: " . ($error ?: '未知错误'));
        }

        return $decrypted;
    }
}
