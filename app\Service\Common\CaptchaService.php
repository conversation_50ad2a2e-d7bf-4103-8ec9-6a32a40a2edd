<?php

namespace App\Service\Common;

use App\Service\BaseService;
use Facade\Captcha\Captcha;

class CaptchaService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getCaptcha(){
        $captcha = app('captcha')->create('default', true);
        return [
            'image_key' => $captcha['key'],
            'image_base64' => $captcha['img'],
        ];


    }
    
}
