import { API_DOMAIN, API_BASE } from '../env.js'
import { getToken, removeToken } from '../utils/storage.js'

// 此vm参数为页面的实例,可以通过它引用vuex中的变量
module.exports = (vm) => {
    // 初始化请求配置
    uni.$u.http.setConfig((config) => {
        config.baseURL = API_DOMAIN + API_BASE
        config.timeout = 30000
        config.header = {
            'Content-Type': 'application/json;charset=UTF-8'
        }
        return config
    })

    // 请求拦截
    uni.$u.http.interceptors.request.use((config) => {
        // 初始化请求拦截器时,会执行此方法,此时data为undefined,赋予默认{}
        config.data = config.data || {}
        // 获取token,注意:如果需要token,需要在请求时设置custom.auth为true
        if(config?.custom?.auth) {
            const token = getToken()
            if (token) {
                config.header['Authorization'] = `Bearer ${token}`
            }
        }
        return config
    }, config => {
        return Promise.reject(config)
    })

    // 响应拦截
    uni.$u.http.interceptors.response.use((response) => {
        const data = response.data
        // 根据自定义状态码处理
        if (data.status === 200) {
            // 增加code判断
            if(data.code === 1) {
                return data.data === undefined ? {} : data.data
            } else {
                uni.showToast({
                    title: data.msg || '操作失败',
                    icon: 'none'
                });
                return Promise.reject(new Error(data.msg || '操作失败'));
            }
        } else if (data.status === 401) {
            // 401表示token过期,需要重新登录
            uni.showToast({
                title: '请重新登录',
                icon: 'none'
            })
            // 清除token并跳转登录页
            removeToken()
            uni.navigateTo({
                url: '/pages/login/login'
            });
            return Promise.reject(new Error('请重新登录'));
        } else {
            uni.showToast({
                title: data.msg || '操作失败', 
                icon: 'none'
            });
            return Promise.reject(new Error(res.msg || '操作失败'))
        }
    }, (response) => {
        //状态码不是200,这里会执行
        const data = response.data;
        uni.showToast({
            title: data.msg || '请求失败',
            icon: 'none'
        });
        return Promise.reject(response);
    })
} 