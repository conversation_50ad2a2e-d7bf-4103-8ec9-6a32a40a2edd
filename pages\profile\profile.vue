<template>
	<view class="page-container">
		<!-- 内容区域 - 固定高度布局 -->
		<view class="content-wrapper">
				<!-- 用户信息头部区域 -->
				<view class="user-header-section">
					<!-- 用户基本信息 -->
					<view class="user-info-card">
						<view class="user-avatar-container">
							<image 
								:src="userInfo.avatar" 
								class="user-avatar"
								mode="aspectFill"
							></image>
							<view class="avatar-border"></view>
						</view>
						<view class="user-details">
							<view class="user-name">{{ userInfo.nickname }}</view>
							<view class="user-id-row">
								<text class="id-label">ID: {{ userInfo.id }}</text>
								<view class="copy-btn" @click="copyUserId">
									<text class="copy-icon">📋</text>
								</view>
							</view>
						</view>
					</view>

				</view>

							<!-- 功能入口区域 -->
			<view class="function-section">
				<view class="function-grid">
						<view 
							v-for="(item, index) in functionList" 
							:key="index"
							class="function-item"
							@click="handleFunctionClick(item)"
						>
							<view class="function-icon" :class="item.iconClass">
								<text class="icon-symbol">{{ item.icon }}</text>
							</view>
							<view class="function-info">
								<text class="function-name">{{ item.name }}</text>
								<text class="function-desc">{{ item.desc }}</text>
							</view>
						</view>
					</view>
				</view>



							<!-- 底部安全区域 -->
			<view class="safe-area-bottom"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					nickname: '用户昵称',
					id: '238947',
					avatar: 'https://placehold.co/200x200'
				},
				functionList: [
					{
						name: '我的报告',
						desc: '已提交12份',
						icon: '📄',
						iconClass: 'icon-report',
						action: 'reports'
					},
					{
						name: '我的线索',
						desc: '5条待审核',
						icon: '💧',
						iconClass: 'icon-clue',
						action: 'clues'
					},
					{
						name: '退出登录',
						desc: '安全退出账号',
						icon: '🚪',
						iconClass: 'icon-logout',
						action: 'logout'
					}
				]
			}
		},
		methods: {
			// 加载用户数据
			loadUserData() {
				// 这里可以调用API获取用户数据
				console.log('加载用户数据')
			},
			
			// 复制用户ID
			copyUserId() {
				uni.setClipboardData({
					data: this.userInfo.id,
					success: () => {
						uni.showToast({
							title: '用户ID已复制',
							icon: 'success'
						})
					}
				})
			},
			

			
			// 退出登录
			handleLogout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出当前账号吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除本地存储的用户信息
							uni.removeStorageSync('userInfo')
							uni.removeStorageSync('token')
							
							// 跳转到登录页面
							uni.reLaunch({
								url: '/pages/login/login'
							})
						}
					}
				})
			},
			
			// 功能点击
			handleFunctionClick(item) {
				console.log('功能点击:', item.name)
				
				switch(item.action) {
					case 'reports':
						uni.showToast({
							title: '跳转到我的报告',
							icon: 'none'
						})
						break
					case 'clues':
						uni.showToast({
							title: '跳转到我的线索',
							icon: 'none'
						})
						break
					case 'logout':
						this.handleLogout()
						break
					default:
						uni.showToast({
							title: item.name,
							icon: 'none'
						})
				}
			}
		},
		
		onLoad() {
			// 页面加载时获取用户数据
			this.loadUserData()
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
}

/* 内容区域 */
.content-wrapper {
	height: 100vh;
	color: #ffffff;
	display: flex;
	flex-direction: column;
}

/* 用户信息头部区域 */
.user-header-section {
	padding: 60rpx 40rpx 32rpx;
	
	/* 用户基本信息卡片 */
	.user-info-card {
		display: flex;
		align-items: center;
		
		.user-avatar-container {
			position: relative;
			margin-right: 32rpx;
			width: 160rpx;
			height: 160rpx;
			
			.user-avatar {
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background-color: #e5e7eb;
				display: block;
			}
			
			.avatar-border {
				position: absolute;
				top: -8rpx;
				left: -8rpx;
				width: calc(100% + 16rpx);
				height: calc(100% + 16rpx);
				border: 4rpx solid #fbbf24;
				border-radius: 50%;
				pointer-events: none;
				box-sizing: border-box;
			}
		}
		
		.user-details {
			flex: 1;
			
			.user-name {
				font-size: 40rpx;
				font-weight: bold;
				color: #ffffff;
				margin-bottom: 8rpx;
			}
			
			.user-id-row {
				display: flex;
				align-items: center;
				
				.id-label {
					font-size: 28rpx;
					color: #ffffff;
					margin-right: 16rpx;
				}
				
				.copy-btn {
					padding: 8rpx;
					
					.copy-icon {
						font-size: 24rpx;
						color: #ffffff;
					}
				}
			}
		}
	}
}

/* 功能入口区域 */
.function-section {
	margin: 32rpx 40rpx;
	
	.function-grid {
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		
		.function-item {
			background: rgba(255, 255, 255, 0.1);
			border-radius: 24rpx;
			padding: 40rpx 32rpx;
			display: flex;
			align-items: center;
			backdrop-filter: blur(10rpx);
			
			.function-icon {
				width: 80rpx;
				height: 80rpx;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 32rpx;
				
				&.icon-report {
					background: rgba(251, 191, 36, 0.2);
				}
				
				&.icon-clue {
					background: rgba(59, 130, 246, 0.2);
				}
				
				&.icon-logout {
					background: rgba(239, 68, 68, 0.2);
				}
				
				.icon-symbol {
					font-size: 40rpx;
				}
			}
			
			.function-info {
				flex: 1;
				
				.function-name {
					display: block;
					font-size: 36rpx;
					font-weight: 600;
					color: #ffffff;
					margin-bottom: 8rpx;
				}
				
				.function-desc {
					display: block;
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.7);
				}
			}
		}
	}
}

/* 底部安全区域 */
.safe-area-bottom {
	height: 120rpx; /* 为底部导航留出空间 */
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style>
