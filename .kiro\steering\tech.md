# Technology Stack

## Framework & Core Technologies
- **uni-app**: Cross-platform development framework
- **Vue 2**: JavaScript framework (with Vue 3 compatibility)
- **uView UI**: Vue component library for uni-app
- **SCSS**: CSS preprocessor for styling

## Key Dependencies
- **uqrcodejs** (^4.0.7): QR code generation library
- **uview-ui** (^2.0.38): UI component library

## Architecture Patterns
- **Pages-based routing**: Each page in `pages/` directory with corresponding route in `pages.json`
- **Custom navigation**: All pages use `navigationStyle: "custom"` for consistent UI
- **Tab-based navigation**: Bottom tab bar with 5 main sections
- **Token-based authentication**: JWT-style bearer token system
- **Centralized HTTP configuration**: Request/response interceptors in `config/request.js`

## Environment Configuration
- **Development**: `http://citui.test.com`
- **Production**: Dynamic origin detection with fallback
- **API Base**: `/api` endpoint prefix
- **Environment switching**: Based on `process.env.NODE_ENV`

## Build & Development Commands
Since this is a uni-app project, typical commands would be:
```bash
# Install dependencies
npm install

# Development (specific platform)
npm run dev:mp-weixin    # WeChat Mini Program
npm run dev:h5           # H5 web
npm run dev:app-plus     # Native app

# Build for production
npm run build:mp-weixin  # WeChat Mini Program
npm run build:h5         # H5 web
npm run build:app-plus   # Native app
```

## Code Style Conventions
- **rpx units**: Default unit for responsive design
- **SCSS styling**: Global styles in `App.vue` and `uni.scss`
- **Safe area handling**: CSS variables for device compatibility
- **Custom components**: Use uView UI prefix `u-` for components