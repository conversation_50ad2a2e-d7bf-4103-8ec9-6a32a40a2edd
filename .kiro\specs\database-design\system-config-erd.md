# 系统配置和统计表结构实体关系图

## Mermaid ERD 图表

```mermaid
erDiagram
    ct_system_configs {
        int config_id PK "配置ID"
        varchar config_key UK "配置键名"
        varchar config_name "配置名称"
        text config_value "配置值"
        enum config_type "配置类型"
        varchar config_group "配置分组"
        text config_description "配置描述"
        text default_value "默认值"
        varchar validation_rule "验证规则"
        tinyint is_encrypted "是否加密"
        tinyint is_public "是否公开"
        tinyint is_editable "是否可编辑"
        tinyint is_active "是否启用"
        int sort_order "排序顺序"
        int version "配置版本"
        bigint created_by FK "创建人ID"
        bigint updated_by FK "更新人ID"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_operation_logs {
        bigint log_id PK "日志ID"
        bigint operator_id FK "操作人ID"
        enum operator_type "操作人类型"
        varchar operator_name "操作人名称"
        varchar module "操作模块"
        varchar action "操作动作"
        varchar resource_type "资源类型"
        bigint resource_id "资源ID"
        text operation_desc "操作描述"
        varchar request_method "请求方法"
        varchar request_url "请求URL"
        json request_params "请求参数"
        json response_data "响应数据"
        enum operation_result "操作结果"
        text error_message "错误信息"
        varchar ip_address "操作IP"
        varchar user_agent "用户代理"
        varchar session_id "会话ID"
        varchar trace_id "链路追踪ID"
        int processing_time "处理耗时"
        timestamp created_at "创建时间"
    }

    ct_statistics {
        bigint stat_id PK "统计ID"
        varchar stat_key "统计键名"
        varchar stat_name "统计名称"
        enum stat_type "统计类型"
        varchar stat_category "统计分类"
        varchar stat_dimension "统计维度"
        decimal stat_value "统计值"
        bigint stat_count "统计次数"
        decimal stat_sum "统计总和"
        decimal stat_avg "统计平均值"
        decimal stat_min "统计最小值"
        decimal stat_max "统计最大值"
        json stat_data "扩展统计数据"
        enum time_period "时间周期"
        date stat_date "统计日期"
        tinyint stat_hour "统计小时"
        tinyint stat_week "统计周"
        tinyint stat_month "统计月"
        smallint stat_year "统计年"
        tinyint is_calculated "是否已计算"
        timestamp calculation_time "计算时间"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_admin_users {
        bigint admin_id PK "管理员ID"
        varchar username "用户名"
        varchar real_name "真实姓名"
        varchar email "邮箱"
        varchar phone "手机号"
        timestamp created_at "创建时间"
    }

    ct_users {
        bigint user_id PK "用户ID"
        varchar phone "手机号"
        varchar nickname "昵称"
        varchar avatar_url "头像URL"
        timestamp created_at "创建时间"
    }

    %% 关系定义
    ct_admin_users ||--o{ ct_system_configs : "created_by"
    ct_admin_users ||--o{ ct_system_configs : "updated_by"
    ct_users ||--o{ ct_operation_logs : "operator_id (user)"
    ct_admin_users ||--o{ ct_operation_logs : "operator_id (admin)"
```

## 关系说明

### 1. 系统配置管理关系
- **ct_system_configs** 由 **ct_admin_users** 创建和维护
- 支持配置的创建者和最后更新者追踪
- 关系类型：多对一（N:1）

### 2. 操作日志记录关系
- **ct_operation_logs** 可以关联 **ct_users** 或 **ct_admin_users**
- 通过 `operator_type` 字段区分操作人类型
- 支持用户操作、管理员操作、系统操作和API操作
- 关系类型：多对一（N:1）

### 3. 统计数据独立性
- **ct_statistics** 表相对独立，不直接关联其他表
- 通过 `stat_key` 和 `stat_category` 间接关联业务数据
- 支持灵活的统计维度和聚合方式

## 配置分组结构图

```mermaid
graph TD
    A[系统配置] --> B[积分系统 point]
    A --> C[APP管理 app]
    A --> D[审核系统 audit]
    A --> E[文件系统 file]
    A --> F[系统基础 system]
    A --> G[通知系统 notification]
    
    B --> B1[评测奖励积分]
    B --> B2[线索奖励积分]
    B --> B3[签到积分]
    B --> B4[邀请奖励积分]
    
    C --> C1[APP分类配置]
    C --> C2[图标大小限制]
    C --> C3[图标格式限制]
    
    D --> D1[自动审核开关]
    D --> D2[审核超时时间]
    D --> D3[敏感词库]
    D --> D4[审核阈值]
    
    E --> E1[文件大小限制]
    E --> E2[存储类型]
    E --> E3[CDN域名]
    
    F --> F1[系统名称]
    F --> F2[系统版本]
    F --> F3[维护模式]
    F --> F4[注册开关]
    
    G --> G1[短信通知]
    G --> G2[邮件通知]
```

## 操作日志分类图

```mermaid
graph TD
    A[操作日志] --> B[用户操作 user]
    A --> C[管理员操作 admin]
    A --> D[系统操作 system]
    A --> E[API操作 api]
    
    B --> B1[用户注册]
    B --> B2[内容提交]
    B --> B3[积分兑换]
    B --> B4[资料修改]
    
    C --> C1[配置修改]
    C --> C2[内容审核]
    C --> C3[用户管理]
    C --> C4[系统维护]
    
    D --> D1[定时任务]
    D --> D2[数据同步]
    D --> D3[自动审核]
    D --> D4[统计计算]
    
    E --> E1[第三方调用]
    E --> E2[数据接口]
    E --> E3[回调通知]
    E --> E4[批量操作]
```

## 统计数据维度图

```mermaid
graph TD
    A[统计数据] --> B[时间维度]
    A --> C[业务维度]
    A --> D[统计类型]
    
    B --> B1[实时 realtime]
    B --> B2[小时 hourly]
    B --> B3[日 daily]
    B --> B4[周 weekly]
    B --> B5[月 monthly]
    B --> B6[年 yearly]
    
    C --> C1[用户活跃度]
    C --> C2[内容提交量]
    C --> C3[积分发放量]
    C --> C4[审核处理量]
    C --> C5[文件上传量]
    
    D --> D1[计数器 counter]
    D --> D2[仪表 gauge]
    D --> D3[直方图 histogram]
    D --> D4[摘要 summary]
```

## 索引策略

### 主键索引
- 所有表都有自增主键，提供唯一标识和高效查询

### 外键索引
- `ct_system_configs.created_by` → `ct_admin_users.admin_id`
- `ct_system_configs.updated_by` → `ct_admin_users.admin_id`
- `ct_operation_logs.operator_id` → `ct_users.user_id` 或 `ct_admin_users.admin_id`

### 业务查询索引
- `ct_system_configs.config_key`：配置键名查询（唯一索引）
- `ct_system_configs.config_group`：配置分组查询
- `ct_operation_logs.module`：操作模块查询
- `ct_statistics.stat_key`：统计键名查询

### 复合索引
- `(config_group, is_active)`：活跃配置分组查询
- `(module, action)`：操作模块和动作查询
- `(stat_key, time_period, stat_date)`：统计时间序列查询
- `(operator_id, created_at)`：操作人时间序列查询

### 时间索引
- `ct_system_configs.updated_at`：配置更新时间
- `ct_operation_logs.created_at`：操作时间查询
- `ct_statistics.stat_date`：统计日期查询

## 数据完整性约束

### 外键约束
1. **级联设置NULL**：管理员删除时相关记录设为NULL
2. **限制删除**：有关联数据的记录不能被删除

### 唯一性约束
1. **配置键唯一**：`ct_system_configs.config_key`
2. **统计数据唯一**：`(stat_key, stat_category, stat_dimension, time_period, stat_date, stat_hour)`

### 检查约束
1. **枚举值检查**：config_type, operator_type, stat_type 等枚举字段
2. **数值范围检查**：version >= 1, processing_time >= 0
3. **时间逻辑检查**：updated_at >= created_at

## 触发器机制

### 配置变更触发器
- 配置值变更时自动记录操作日志
- 自动更新配置版本号
- 支持配置变更的完整审计

### 统计计算触发器
- 自动设置时间维度字段（小时、周、月、年）
- 自动计算统计平均值
- 标记统计数据的计算状态

## 存储过程支持

### 配置管理
- `sp_get_config_value`: 安全获取配置值
- `sp_update_config_value`: 安全更新配置值

### 日志记录
- `sp_log_operation`: 标准化操作日志记录

### 统计更新
- `sp_update_statistics`: 增量统计数据更新

## 查询模式

### 常用查询场景
1. **获取系统配置列表**
2. **查询操作日志历史**
3. **获取统计数据报表**
4. **配置变更历史追踪**
5. **用户操作行为分析**

### 性能优化建议
1. 使用复合索引优化多条件查询
2. 合理使用分页避免大结果集
3. 预聚合统计数据减少实时计算
4. 使用缓存提升配置访问速度
5. 定期归档历史数据保持表大小合理

## 扩展性考虑

### 水平扩展
- 操作日志表可按时间分区
- 统计数据表可按类别分片
- 支持读写分离架构

### 功能扩展
- 支持配置模板和批量配置
- 集成实时日志流处理
- 支持自定义统计指标
- 集成机器学习分析

### 性能扩展
- 支持分布式配置管理
- 集成消息队列系统
- 支持实时统计计算
- 优化大数据量查询性能