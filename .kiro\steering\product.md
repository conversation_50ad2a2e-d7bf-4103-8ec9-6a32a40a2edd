# Product Overview

**次推** (CitUI) is a Chinese mobile application built with uni-app framework. The app appears to be a platform for evaluation, clues, and invitations with the following main features:

## Core Features
- **首页 (Home)**: Main dashboard and content discovery
- **评测 (Evaluation)**: App/task evaluation system
- **线索 (Clues)**: Clue submission and management
- **邀请 (Invite)**: User invitation system  
- **我的 (Profile)**: User profile and account management

## Key Functionality
- User authentication and token-based authorization
- Search functionality for apps and tasks
- Report submission system
- QR code generation (using uqrcodejs)
- Multi-platform support (WeChat Mini Program, H5, App)

## Target Platforms
- iOS and Android native apps
- WeChat Mini Program
- H5 web application
- Other mini-program platforms (Alipay, Baidu, Toutiao)