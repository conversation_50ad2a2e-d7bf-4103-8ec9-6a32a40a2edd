<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "uview-ui/index.scss";

	/* 全局样式 */
	page {
		background-color: #f5f5f5;
	}

	/* 页面内容区域样式，为tabbar预留空间 */
	.page-content {
		padding-bottom: 60px; /* tabbar高度 */
	}

	/* 安全区域适配 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
