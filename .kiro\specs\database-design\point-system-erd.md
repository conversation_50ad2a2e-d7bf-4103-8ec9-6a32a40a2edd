# 积分和奖励系统 ER 图

## 实体关系图

```mermaid
erDiagram
    ct_users ||--o{ ct_point_records : "用户积分记录"
    ct_users ||--|| ct_user_points : "用户积分汇总"
    ct_users ||--o{ ct_point_exchanges : "用户兑换记录"
    
    ct_point_rules ||--o{ ct_point_records : "规则应用记录"
    ct_reward_configs ||--o{ ct_point_exchanges : "配置兑换记录"
    
    ct_evaluation_reports ||--o{ ct_point_records : "评测积分记录"
    ct_water_clues ||--o{ ct_point_records : "线索积分记录"
    
    ct_users {
        bigint id PK "用户ID"
        varchar phone "手机号"
        varchar nickname "昵称"
        tinyint status "用户状态"
    }
    
    ct_point_records {
        bigint id PK "记录ID"
        bigint user_id FK "用户ID"
        int point_change "积分变动"
        int point_balance "变动后余额"
        varchar change_type "变动类型"
        varchar change_reason "变动原因"
        varchar related_type "关联业务类型"
        bigint related_id "关联业务ID"
        bigint operator_id "操作人ID"
        tinyint status "记录状态"
        timestamp created_at "创建时间"
    }
    
    ct_point_rules {
        int id PK "规则ID"
        varchar rule_code UK "规则代码"
        varchar rule_name "规则名称"
        varchar rule_description "规则描述"
        int point_value "积分数值"
        varchar trigger_condition "触发条件"
        int daily_limit "每日限制"
        int total_limit "总限制"
        date valid_start_date "生效开始日期"
        date valid_end_date "生效结束日期"
        int priority "优先级"
        tinyint status "规则状态"
        timestamp created_at "创建时间"
    }
    
    ct_reward_configs {
        int id PK "配置ID"
        varchar config_code UK "配置代码"
        varchar config_name "配置名称"
        varchar config_type "配置类型"
        varchar reward_type "奖励类型"
        varchar reward_value "奖励内容"
        int required_points "所需积分"
        int required_level "所需等级"
        int exchange_limit "兑换限制"
        int daily_limit "每日限制"
        int stock_quantity "库存数量"
        int sort_order "排序"
        tinyint status "配置状态"
        timestamp start_time "生效开始时间"
        timestamp end_time "生效结束时间"
        timestamp created_at "创建时间"
    }
    
    ct_user_points {
        bigint user_id PK,FK "用户ID"
        int total_points "总积分"
        int available_points "可用积分"
        int frozen_points "冻结积分"
        int total_earned "累计获得"
        int total_consumed "累计消耗"
        int level "用户等级"
        int level_progress "等级进度"
        int next_level_points "升级所需积分"
        timestamp last_point_time "最后积分变动时间"
        timestamp created_at "创建时间"
    }
    
    ct_point_exchanges {
        bigint id PK "兑换记录ID"
        bigint user_id FK "用户ID"
        int reward_config_id FK "奖励配置ID"
        int exchange_points "兑换消耗积分"
        varchar reward_content "奖励内容"
        tinyint exchange_status "兑换状态"
        timestamp exchange_time "兑换时间"
        timestamp process_time "处理时间"
        bigint process_by "处理人ID"
        timestamp created_at "创建时间"
    }
    
    ct_evaluation_reports {
        bigint id PK "报告ID"
        bigint user_id FK "用户ID"
        varchar app_name "APP名称"
        tinyint audit_status "审核状态"
    }
    
    ct_water_clues {
        bigint id PK "线索ID"
        bigint user_id FK "用户ID"
        varchar app_name "APP名称"
        tinyint audit_status "审核状态"
    }
```

## 关系说明

### 主要关系

1. **用户 - 积分记录 (1:N)**
   - 一个用户可以有多条积分变动记录
   - 每条积分记录属于一个用户

2. **用户 - 积分汇总 (1:1)**
   - 每个用户有且仅有一条积分汇总记录
   - 用于快速查询用户当前积分状态

3. **用户 - 兑换记录 (1:N)**
   - 一个用户可以有多条兑换记录
   - 每条兑换记录属于一个用户

4. **积分规则 - 积分记录 (1:N)**
   - 一个积分规则可以对应多条积分记录
   - 每条积分记录基于一个积分规则生成

5. **奖励配置 - 兑换记录 (1:N)**
   - 一个奖励配置可以对应多条兑换记录
   - 每条兑换记录基于一个奖励配置

### 业务关联

1. **评测报告 - 积分记录**
   - 用户提交评测报告时生成积分记录
   - 通过 related_type='evaluation' 和 related_id 关联

2. **放水线索 - 积分记录**
   - 用户提交放水线索时生成积分记录
   - 通过 related_type='clue' 和 related_id 关联

## 约束说明

### 外键约束
- `ct_point_records.user_id` → `ct_users.id`
- `ct_user_points.user_id` → `ct_users.id`
- `ct_point_exchanges.user_id` → `ct_users.id`
- `ct_point_exchanges.reward_config_id` → `ct_reward_configs.id`

### 唯一约束
- `ct_point_rules.rule_code` - 规则代码唯一
- `ct_reward_configs.config_code` - 配置代码唯一
- `ct_user_points.user_id` - 用户积分汇总唯一

### 检查约束
- `ct_point_records.point_change` - 积分变动不能为0
- `ct_user_points.available_points` - 可用积分不能为负数
- `ct_reward_configs.required_points` - 所需积分必须大于0
- `ct_point_exchanges.exchange_points` - 兑换积分必须大于0

## 索引策略

### 主要索引
- **性能索引**：用户ID、创建时间、状态等高频查询字段
- **业务索引**：变动类型、配置类型、兑换状态等业务查询字段
- **复合索引**：关联业务类型+ID、时间范围等复合查询字段

### 查询优化
- 使用积分汇总表避免实时计算
- 通过状态字段快速过滤
- 使用时间索引优化历史查询