# 文件管理系统实体关系图

## Mermaid ERD 图表

```mermaid
erDiagram
    ct_file_categories {
        int category_id PK "分类ID"
        varchar category_name "分类名称"
        varchar category_code UK "分类代码"
        int parent_id FK "父分类ID"
        int sort_order "排序顺序"
        bigint max_file_size "最大文件大小"
        varchar allowed_extensions "允许的扩展名"
        varchar description "分类描述"
        tinyint is_active "是否启用"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_files {
        bigint file_id PK "文件ID"
        int category_id FK "文件分类ID"
        bigint uploader_id FK "上传用户ID"
        varchar original_name "原始文件名"
        varchar stored_name "存储文件名"
        varchar file_path "文件存储路径"
        varchar file_url "文件访问URL"
        bigint file_size "文件大小"
        varchar file_type "文件MIME类型"
        varchar file_extension "文件扩展名"
        varchar file_hash "文件MD5哈希"
        int width "图片宽度"
        int height "图片高度"
        int duration "视频时长"
        varchar upload_ip "上传IP"
        varchar upload_device "上传设备"
        enum storage_type "存储类型"
        tinyint is_public "是否公开"
        tinyint is_deleted "是否删除"
        timestamp deleted_at "删除时间"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_file_relations {
        bigint relation_id PK "关联ID"
        bigint file_id FK "文件ID"
        varchar business_type "业务类型"
        bigint business_id "业务对象ID"
        varchar relation_type "关联类型"
        int sort_order "排序顺序"
        tinyint is_primary "是否主要文件"
        varchar description "关联描述"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_users {
        bigint user_id PK "用户ID"
        varchar phone "手机号"
        varchar nickname "昵称"
        varchar avatar_url "头像URL"
        timestamp created_at "创建时间"
    }

    ct_apps {
        bigint app_id PK "应用ID"
        varchar app_name "应用名称"
        varchar logo_url "Logo URL"
        timestamp created_at "创建时间"
    }

    ct_evaluation_reports {
        bigint report_id PK "报告ID"
        bigint app_id FK "应用ID"
        bigint user_id FK "用户ID"
        timestamp created_at "创建时间"
    }

    ct_water_clues {
        bigint clue_id PK "线索ID"
        bigint app_id FK "应用ID"
        bigint user_id FK "用户ID"
        timestamp created_at "创建时间"
    }

    %% 关系定义
    ct_file_categories ||--o{ ct_file_categories : "parent_id"
    ct_file_categories ||--o{ ct_files : "category_id"
    ct_users ||--o{ ct_files : "uploader_id"
    ct_files ||--o{ ct_file_relations : "file_id"
    
    %% 业务对象关联（通过file_relations表）
    ct_apps ||--o{ ct_file_relations : "business_id (app_logo)"
    ct_evaluation_reports ||--o{ ct_file_relations : "business_id (evaluation_screenshot)"
    ct_water_clues ||--o{ ct_file_relations : "business_id (clue_screenshot)"
    ct_users ||--o{ ct_file_relations : "business_id (user_avatar)"
```

## 关系说明

### 1. 分类层级关系
- **ct_file_categories** 自关联：支持无限层级的分类结构
- 通过 `parent_id` 字段建立父子关系
- 根分类的 `parent_id` 为 NULL

### 2. 文件归属关系
- **ct_files** 属于 **ct_file_categories**：每个文件必须归属于一个分类
- **ct_files** 属于 **ct_users**：每个文件都有明确的上传者
- 关系类型：多对一（N:1）

### 3. 文件关联关系
- **ct_file_relations** 连接 **ct_files** 和各种业务对象
- 支持多对多关联：一个文件可以关联多个业务对象，一个业务对象可以关联多个文件
- 通过 `business_type` 和 `business_id` 实现多态关联

### 4. 业务对象关联
通过 `ct_file_relations` 表实现的业务关联：

#### APP图标关联
- `business_type = 'app_logo'`
- `business_id` 指向 `ct_apps.app_id`
- 关联类型：一个APP可以有多个版本的Logo

#### 评测截图关联
- `business_type = 'evaluation_screenshot'`
- `business_id` 指向 `ct_evaluation_reports.report_id`
- 关联类型：一个评测报告可以包含多张截图

#### 线索截图关联
- `business_type = 'clue_screenshot'`
- `business_id` 指向 `ct_water_clues.clue_id`
- 关联类型：一个线索可以包含多张截图

#### 用户头像关联
- `business_type = 'user_avatar'`
- `business_id` 指向 `ct_users.user_id`
- 关联类型：一个用户可以有多个历史头像

## 索引策略

### 主键索引
- 所有表都有自增主键，提供唯一标识

### 外键索引
- `ct_files.category_id` → `ct_file_categories.category_id`
- `ct_files.uploader_id` → `ct_users.user_id`
- `ct_file_relations.file_id` → `ct_files.file_id`

### 业务查询索引
- `ct_file_categories.category_code`：按分类代码查询
- `ct_files.file_hash`：文件去重查询
- `ct_file_relations.business_type_id`：业务对象文件查询

### 复合索引
- `(uploader_id, category_id)`：用户分类文件查询
- `(business_type, business_id)`：业务对象关联查询
- `(is_deleted, created_at)`：有效文件时间查询

## 数据完整性约束

### 外键约束
1. **级联删除**：文件删除时自动清理关联关系
2. **限制删除**：有文件的分类不能被删除
3. **设置NULL**：父分类删除时子分类的parent_id设为NULL

### 唯一性约束
1. **分类代码唯一**：`ct_file_categories.category_code`
2. **文件业务关联唯一**：`(file_id, business_type, business_id, relation_type)`

### 检查约束
1. **文件大小**：必须大于0且不超过分类限制
2. **状态值**：is_active, is_public, is_deleted 只能为0或1
3. **存储类型**：只能是预定义的存储类型枚举值

## 触发器机制

### 软删除触发器
- 文件软删除时自动设置 `deleted_at` 时间戳
- 软删除恢复时清空 `deleted_at` 字段

### 关联清理触发器
- 文件被软删除时自动清理 `ct_file_relations` 中的关联记录
- 保证数据一致性，避免无效关联

## 查询模式

### 常用查询场景
1. **获取用户上传的文件列表**
2. **获取特定业务对象的关联文件**
3. **按分类浏览文件**
4. **文件去重检查**
5. **获取文件的完整信息（包含分类和上传者）**

### 性能优化建议
1. 使用覆盖索引减少回表查询
2. 合理使用分页避免大结果集
3. 预加载关联数据减少N+1查询
4. 使用缓存提升热点数据访问速度