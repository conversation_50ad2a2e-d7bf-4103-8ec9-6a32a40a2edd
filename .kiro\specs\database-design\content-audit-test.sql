-- =============================================
-- 内容审核系统测试SQL脚本
-- 创建时间: 2025-01-08
-- 描述: 验证内容审核系统表结构和功能的完整性测试
-- =============================================

-- 测试环境准备
SET FOREIGN_KEY_CHECKS = 0;

-- 清理测试数据（如果存在）
DROP TABLE IF EXISTS ct_audit_logs;
DROP TABLE IF EXISTS ct_content_audits;
DROP TABLE IF EXISTS ct_audit_rules;

-- 重新创建表结构（这里假设已经执行了content-audit-tables.sql）

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 测试数据准备
-- =============================================

-- 1. 测试审核规则管理
SELECT '=== 测试1: 审核规则管理 ===' as test_name;

-- 验证初始规则数据
SELECT 
    rule_id,
    rule_name,
    rule_code,
    content_type,
    auto_audit_enabled,
    manual_audit_required,
    audit_timeout_hours,
    reward_points,
    penalty_points
FROM ct_audit_rules 
ORDER BY sort_order;

-- 添加自定义审核规则测试
INSERT INTO ct_audit_rules (
    rule_name, rule_code, content_type, rule_description,
    auto_audit_enabled, auto_audit_keywords, manual_audit_required,
    audit_timeout_hours, pass_score_threshold, reject_score_threshold,
    reward_points, penalty_points, sort_order
) VALUES (
    '评论内容审核', 'comment_audit', 'comment', '用户评论内容审核规则',
    1, '["违法", "色情", "暴力", "广告", "垃圾"]', 1,
    12, 70, 30, 2, 1, 6
);

-- 验证规则查询功能
SELECT 
    rule_name,
    content_type,
    CASE 
        WHEN auto_audit_enabled = 1 THEN '启用自动审核'
        ELSE '仅人工审核'
    END as audit_mode,
    audit_timeout_hours as timeout_hours,
    reward_points,
    penalty_points
FROM ct_audit_rules 
WHERE is_active = 1
ORDER BY sort_order;

-- =============================================
-- 测试2: 内容审核流程
-- =============================================

SELECT '=== 测试2: 内容审核流程 ===' as test_name;

-- 模拟管理员数据
INSERT IGNORE INTO ct_admin_users (admin_id, username, real_name, email, phone, created_at) VALUES
(1001, 'admin001', '张审核', '<EMAIL>', '13800001001', NOW()),
(1002, 'admin002', '李审核', '<EMAIL>', '13800001002', NOW()),
(1003, 'admin003', '王审核', '<EMAIL>', '13800001003', NOW());

-- 模拟业务数据
INSERT IGNORE INTO ct_evaluation_reports (report_id, app_id, user_id, report_title, report_content, created_at) VALUES
(5001, 2001, 1001, '微信评测报告', '这是一个详细的微信APP评测报告内容...', NOW()),
(5002, 2002, 1002, '支付宝评测报告', '这是一个详细的支付宝APP评测报告内容...', NOW());

INSERT IGNORE INTO ct_water_clues (clue_id, app_id, user_id, clue_title, clue_content, created_at) VALUES
(6001, 2001, 1002, '微信放水线索', '发现微信某功能存在放水机会...', NOW()),
(6002, 2002, 1003, '支付宝放水线索', '发现支付宝某活动存在漏洞...', NOW());

-- 提交内容审核测试
INSERT INTO ct_content_audits (
    rule_id, content_type, content_id, submitter_id, 
    auto_audit_score, auto_audit_result, auto_audit_reason,
    manual_audit_required, priority_level, content_snapshot
) VALUES
-- 评测报告审核
(1, 'evaluation_report', 5001, 1001, 85, 'pass', '自动审核通过：内容质量良好', 0, 'normal', 
 JSON_OBJECT('title', '微信评测报告', 'content_length', 500, 'keywords_matched', 0)),

(1, 'evaluation_report', 5002, 1002, 45, 'manual', '自动审核需人工确认：检测到敏感词汇', 1, 'high',
 JSON_OBJECT('title', '支付宝评测报告', 'content_length', 300, 'keywords_matched', 1)),

-- 放水线索审核
(2, 'water_clue', 6001, 1002, 25, 'reject', '自动审核拒绝：内容质量不符合要求', 0, 'normal',
 JSON_OBJECT('title', '微信放水线索', 'content_length', 200, 'keywords_matched', 2)),

(2, 'water_clue', 6002, 1003, 60, 'manual', '自动审核需人工确认：内容需要专业判断', 1, 'urgent',
 JSON_OBJECT('title', '支付宝放水线索', 'content_length', 400, 'keywords_matched', 0));

-- 验证审核记录创建
SELECT 
    ca.audit_id,
    ar.rule_name,
    ca.content_type,
    ca.content_id,
    u.nickname as submitter,
    ca.audit_status,
    ca.auto_audit_result,
    ca.priority_level,
    ca.submitted_at
FROM ct_content_audits ca
JOIN ct_audit_rules ar ON ca.rule_id = ar.rule_id
JOIN ct_users u ON ca.submitter_id = u.user_id
ORDER BY ca.submitted_at DESC;

-- =============================================
-- 测试3: 人工审核处理
-- =============================================

SELECT '=== 测试3: 人工审核处理 ===' as test_name;

-- 分配审核员并开始审核
UPDATE ct_content_audits 
SET 
    auditor_id = 1001,
    audit_status = 'reviewing',
    audit_started_at = NOW()
WHERE audit_id = 2 AND manual_audit_required = 1;

UPDATE ct_content_audits 
SET 
    auditor_id = 1002,
    audit_status = 'reviewing',
    audit_started_at = NOW()
WHERE audit_id = 4 AND manual_audit_required = 1;

-- 完成人工审核
UPDATE ct_content_audits 
SET 
    audit_status = 'passed',
    audit_result = 'pass',
    audit_reason = '人工审核通过：内容质量符合要求，敏感词为误判',
    audit_score = 80,
    audit_completed_at = NOW()
WHERE audit_id = 2;

UPDATE ct_content_audits 
SET 
    audit_status = 'rejected',
    audit_result = 'reject',
    audit_reason = '人工审核拒绝：线索信息不够详细，缺乏可操作性',
    audit_score = 30,
    reject_category = 'insufficient_info',
    audit_completed_at = NOW()
WHERE audit_id = 4;

-- 验证审核结果
SELECT 
    ca.audit_id,
    ca.content_type,
    ca.audit_status,
    ca.audit_result,
    au.real_name as auditor,
    ca.audit_score,
    ca.audit_reason,
    TIMESTAMPDIFF(MINUTE, ca.audit_started_at, ca.audit_completed_at) as review_minutes
FROM ct_content_audits ca
LEFT JOIN ct_admin_users au ON ca.auditor_id = au.admin_id
WHERE ca.audit_status IN ('passed', 'rejected')
ORDER BY ca.audit_completed_at DESC;

-- =============================================
-- 测试4: 审核日志验证
-- =============================================

SELECT '=== 测试4: 审核日志验证 ===' as test_name;

-- 验证触发器生成的日志
SELECT 
    al.log_id,
    al.audit_id,
    au.real_name as operator,
    al.operator_type,
    al.action_type,
    al.old_status,
    al.new_status,
    al.action_reason,
    al.created_at
FROM ct_audit_logs al
LEFT JOIN ct_admin_users au ON al.operator_id = au.admin_id
ORDER BY al.audit_id, al.created_at;

-- 手动添加一些操作日志测试
INSERT INTO ct_audit_logs (
    audit_id, operator_id, operator_type, action_type, 
    old_status, new_status, action_reason, ip_address, processing_time
) VALUES
(1, 1001, 'admin', 'auto_approve', 'pending', 'passed', '自动审核通过处理', '*************', 150),
(3, NULL, 'system', 'auto_reject', 'pending', 'rejected', '自动审核拒绝处理', '*************', 200);

-- 验证完整的审核流程日志
SELECT 
    ca.audit_id,
    ca.content_type,
    ca.content_id,
    GROUP_CONCAT(
        CONCAT(al.action_type, '(', al.old_status, '->', al.new_status, ')') 
        ORDER BY al.created_at SEPARATOR ' -> '
    ) as audit_flow
FROM ct_content_audits ca
JOIN ct_audit_logs al ON ca.audit_id = al.audit_id
GROUP BY ca.audit_id, ca.content_type, ca.content_id
ORDER BY ca.audit_id;

-- =============================================
-- 测试5: 超时处理测试
-- =============================================

SELECT '=== 测试5: 超时处理测试 ===' as test_name;

-- 模拟超时审核记录
INSERT INTO ct_content_audits (
    rule_id, content_type, content_id, submitter_id,
    audit_status, auditor_id, priority_level,
    submitted_at, audit_started_at, timeout_at
) VALUES (
    1, 'evaluation_report', 5001, 1001,
    'reviewing', 1003, 'normal',
    DATE_SUB(NOW(), INTERVAL 50 HOUR),
    DATE_SUB(NOW(), INTERVAL 49 HOUR),
    DATE_SUB(NOW(), INTERVAL 2 HOUR)
);

-- 查找超时的审核记录
SELECT 
    ca.audit_id,
    ca.content_type,
    ca.content_id,
    au.real_name as auditor,
    ca.audit_status,
    ca.timeout_at,
    TIMESTAMPDIFF(HOUR, ca.timeout_at, NOW()) as overdue_hours
FROM ct_content_audits ca
LEFT JOIN ct_admin_users au ON ca.auditor_id = au.admin_id
WHERE ca.timeout_at < NOW() 
AND ca.audit_status IN ('pending', 'reviewing')
ORDER BY ca.timeout_at;

-- 处理超时记录
UPDATE ct_content_audits 
SET 
    audit_status = 'timeout',
    audit_reason = '审核超时，系统自动处理'
WHERE timeout_at < NOW() 
AND audit_status IN ('pending', 'reviewing');

-- =============================================
-- 测试6: 复杂查询场景
-- =============================================

SELECT '=== 测试6: 复杂查询场景 ===' as test_name;

-- 6.1 审核员工作台查询
SELECT 
    '审核员工作台数据' as query_description;

SELECT 
    au.real_name as auditor,
    ca.priority_level,
    COUNT(*) as pending_count,
    AVG(TIMESTAMPDIFF(HOUR, ca.submitted_at, NOW())) as avg_waiting_hours
FROM ct_content_audits ca
JOIN ct_admin_users au ON ca.auditor_id = au.admin_id
WHERE ca.audit_status = 'reviewing'
GROUP BY au.admin_id, ca.priority_level
ORDER BY ca.priority_level DESC, pending_count DESC;

-- 6.2 内容类型审核统计
SELECT 
    '内容类型审核统计' as query_description;

SELECT 
    ar.rule_name,
    ca.audit_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(PARTITION BY ar.rule_id), 2) as percentage
FROM ct_content_audits ca
JOIN ct_audit_rules ar ON ca.rule_id = ar.rule_id
GROUP BY ar.rule_id, ar.rule_name, ca.audit_status
ORDER BY ar.rule_name, ca.audit_status;

-- 6.3 审核效率分析
SELECT 
    '审核效率分析' as query_description;

SELECT 
    au.real_name as auditor,
    COUNT(*) as completed_count,
    AVG(TIMESTAMPDIFF(MINUTE, ca.audit_started_at, ca.audit_completed_at)) as avg_review_minutes,
    SUM(CASE WHEN ca.audit_result = 'pass' THEN 1 ELSE 0 END) as pass_count,
    SUM(CASE WHEN ca.audit_result = 'reject' THEN 1 ELSE 0 END) as reject_count,
    ROUND(SUM(CASE WHEN ca.audit_result = 'pass' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pass_rate
FROM ct_content_audits ca
JOIN ct_admin_users au ON ca.auditor_id = au.admin_id
WHERE ca.audit_status IN ('passed', 'rejected')
AND ca.audit_completed_at IS NOT NULL
GROUP BY au.admin_id, au.real_name
ORDER BY completed_count DESC;

-- 6.4 用户内容审核历史
SELECT 
    '用户内容审核历史' as query_description;

SELECT 
    u.nickname,
    ca.content_type,
    COUNT(*) as total_submissions,
    SUM(CASE WHEN ca.audit_status = 'passed' THEN 1 ELSE 0 END) as passed_count,
    SUM(CASE WHEN ca.audit_status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
    ROUND(SUM(CASE WHEN ca.audit_status = 'passed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pass_rate
FROM ct_content_audits ca
JOIN ct_users u ON ca.submitter_id = u.user_id
GROUP BY u.user_id, u.nickname, ca.content_type
HAVING total_submissions > 0
ORDER BY u.nickname, ca.content_type;

-- =============================================
-- 测试7: 性能测试查询
-- =============================================

SELECT '=== 测试7: 性能测试查询 ===' as test_name;

-- 7.1 测试索引效果 - 审核状态查询
EXPLAIN SELECT 
    ca.audit_id,
    ca.content_type,
    ca.content_id,
    ca.audit_status,
    ca.priority_level
FROM ct_content_audits ca
WHERE ca.audit_status = 'pending'
AND ca.priority_level IN ('high', 'urgent')
ORDER BY ca.priority_level DESC, ca.submitted_at ASC
LIMIT 20;

-- 7.2 测试复合索引 - 审核员工作查询
EXPLAIN SELECT 
    ca.audit_id,
    ca.content_type,
    ca.audit_status,
    ca.submitted_at
FROM ct_content_audits ca
WHERE ca.auditor_id = 1001
AND ca.audit_status = 'reviewing'
ORDER BY ca.priority_level DESC, ca.submitted_at ASC;

-- 7.3 测试日志查询性能
EXPLAIN SELECT 
    al.log_id,
    al.action_type,
    al.old_status,
    al.new_status,
    al.created_at
FROM ct_audit_logs al
WHERE al.audit_id = 2
ORDER BY al.created_at DESC;

-- =============================================
-- 测试8: 数据完整性验证
-- =============================================

SELECT '=== 测试8: 数据完整性验证 ===' as test_name;

-- 8.1 验证外键约束
-- 尝试插入不存在的规则ID（应该失败）
-- INSERT INTO ct_content_audits (rule_id, content_type, content_id, submitter_id) 
-- VALUES (999, 'test', 1, 1001);

-- 8.2 验证唯一约束
-- 尝试插入重复的内容审核记录（应该失败）
-- INSERT INTO ct_content_audits (rule_id, content_type, content_id, submitter_id) 
-- VALUES (1, 'evaluation_report', 5001, 1001);

-- 8.3 验证触发器功能
-- 测试状态变更是否自动记录日志
UPDATE ct_content_audits 
SET audit_status = 'reviewing' 
WHERE audit_id = 1;

-- 验证是否自动生成了日志记录
SELECT 
    '触发器测试结果' as test_type,
    COUNT(*) as log_count
FROM ct_audit_logs 
WHERE audit_id = 1 
AND action_type = 'start_review';

-- =============================================
-- 测试结果汇总
-- =============================================

SELECT '=== 测试结果汇总 ===' as test_name;

-- 统计各表的数据量
SELECT 
    'ct_audit_rules' as table_name,
    COUNT(*) as record_count
FROM ct_audit_rules
UNION ALL
SELECT 
    'ct_content_audits' as table_name,
    COUNT(*) as record_count
FROM ct_content_audits
UNION ALL
SELECT 
    'ct_audit_logs' as table_name,
    COUNT(*) as record_count
FROM ct_audit_logs;

-- 验证审核流程完整性
SELECT 
    '审核流程完整性检查' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM ct_content_audits ca 
            LEFT JOIN ct_audit_rules ar ON ca.rule_id = ar.rule_id 
            WHERE ar.rule_id IS NULL
        ) THEN '失败：存在无效的规则关联'
        ELSE '通过：所有审核记录都有有效的规则关联'
    END as result
UNION ALL
SELECT 
    '审核日志完整性检查' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM ct_audit_logs al 
            LEFT JOIN ct_content_audits ca ON al.audit_id = ca.audit_id 
            WHERE ca.audit_id IS NULL
        ) THEN '失败：存在无效的审核日志'
        ELSE '通过：所有日志都指向有效的审核记录'
    END as result;

-- 审核状态分布统计
SELECT 
    '审核状态分布' as summary_type,
    audit_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ct_content_audits), 2) as percentage
FROM ct_content_audits
GROUP BY audit_status
ORDER BY count DESC;

-- 清理测试数据（可选）
-- DELETE FROM ct_audit_logs WHERE log_id > 0;
-- DELETE FROM ct_content_audits WHERE audit_id > 0;
-- DELETE FROM ct_audit_rules WHERE rule_id > 5;

SELECT '=== 内容审核系统测试完成 ===' as test_complete;