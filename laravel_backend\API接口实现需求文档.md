# 次推应用 Laravel 9 API接口实现需求文档

## 文档概述

**项目名称**: 次推应用后端API系统  
**技术栈**: Laravel 9 + MySQL  
**文档版本**: 1.0.0  
**创建时间**: 2025-01-08  

本文档基于页面分析和数据库架构设计，为Laravel 9后端项目提供完整的API接口实现需求。开发人员可以仅凭此文档完成所有API接口的开发工作。

## 目录

1. [项目架构概述](#项目架构概述)
2. [数据库表结构说明](#数据库表结构说明)
3. [通用API规范](#通用api规范)
4. [认证授权系统](#认证授权系统)
5. [用户管理模块](#用户管理模块)
6. [APP管理模块](#app管理模块)
7. [评测系统模块](#评测系统模块)
8. [线索系统模块](#线索系统模块)
9. [积分系统模块](#积分系统模块)
10. [文件管理模块](#文件管理模块)
11. [内容审核模块](#内容审核模块)
12. [系统配置模块](#系统配置模块)
13. [数据统计模块](#数据统计模块)
14. [错误处理规范](#错误处理规范)
15. [数据验证规则](#数据验证规则)

---

## 项目架构概述

### 核心业务模块

- **用户管理**: 用户注册、登录、资料管理、关系管理
- **APP管理**: APP信息管理、分类管理、搜索推荐
- **评测系统**: 评测报告提交、审核、展示、统计
- **线索系统**: 放水线索发布、反馈、统计分析
- **积分系统**: 积分获取、消费、兑换、规则管理
- **文件管理**: 文件上传、存储、关联、访问控制
- **内容审核**: 自动审核、人工审核、审核日志
- **系统配置**: 系统参数配置、运营配置管理
- **数据统计**: 多维度数据统计、报表生成

### 技术架构要求

- **框架**: Laravel 9.x
- **数据库**: MySQL 8.0+
- **认证**: Laravel Sanctum (API Token)
- **文件存储**: 支持本地存储和云存储(OSS/COS)
- **缓存**: Redis
- **队列**: Redis Queue
- **日志**: Laravel Log

---

## 数据库表结构说明

### 核心表列表 (23个表)

#### 用户相关表
- `ct_admin_users`: 管理员用户表
- `ct_users`: 普通用户表
- `ct_user_logins`: 用户登录记录表
- `ct_user_relations`: 用户关系表

#### APP相关表
- `ct_app_categories`: APP分类表
- `ct_apps`: APP信息表#### 评
测相关表
- `ct_evaluation_reports`: 评测报告表
- `ct_evaluation_details`: 评测数据详情表

#### 线索相关表
- `ct_water_clues`: 放水线索表
- `ct_clue_feedbacks`: 线索反馈表
- `ct_clue_statistics`: 线索统计表

#### 积分相关表
- `ct_point_rules`: 积分规则表
- `ct_reward_configs`: 奖励配置表
- `ct_point_records`: 积分记录表

#### 文件相关表
- `ct_file_categories`: 文件分类表
- `ct_files`: 文件信息表
- `ct_file_relations`: 文件关联表

#### 审核相关表
- `ct_audit_rules`: 审核规则表
- `ct_content_audits`: 内容审核表
- `ct_audit_logs`: 审核日志表

#### 系统相关表
- `ct_system_configs`: 系统配置表
- `ct_operation_logs`: 操作日志表
- `ct_statistics`: 统计数据表

### 表关系说明

- 用户表与其他业务表通过user_id关联
- 文件通过ct_file_relations表实现多态关联
- 审核系统通过content_type和content_id实现多态审核
- 积分系统通过source_type和source_id实现多态积分记录

---

## 通用API规范

### 基础URL

```
开发环境: http://localhost:8000/api
生产环境: https://api.citui.com/api
```

### HTTP状态码规范

- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

### 统一响应格式

```json
{
    "success": true,
    "message": "操作成功",
    "data": {},
    "meta": {
        "timestamp": "2025-01-08T10:00:00Z",
        "request_id": "uuid-string"
    }
}
```##
# 分页响应格式

```json
{
    "success": true,
    "message": "获取成功",
    "data": {
        "items": [],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 100,
            "last_page": 7,
            "has_more": true
        }
    }
}
```

### 请求头要求

```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {token}  // 需要认证的接口
```

---

## 认证授权系统

### 1. 用户注册

**端点**: `POST /auth/register`

**请求参数**:
```json
{
    "phone": "13800138000",
    "password": "password123",
    "password_confirmation": "password123",
    "nickname": "用户昵称",
    "verification_code": "123456"
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "注册成功",
    "data": {
        "user": {
            "user_id": 1,
            "phone": "13800138000",
            "nickname": "用户昵称",
            "avatar_url": null,
            "total_points": 50,
            "available_points": 50,
            "level": 1,
            "status": "active",
            "created_at": "2025-01-08T10:00:00Z"
        },
        "token": "sanctum-token-string"
    }
}
```

**业务逻辑**:
- 验证手机号格式和唯一性
- 验证短信验证码
- 密码加密存储
- 自动发放注册奖励积分(50分)
- 生成API Token### 2.
 用户登录

**端点**: `POST /auth/login`

**请求参数**:
```json
{
    "phone": "13800138000",
    "password": "password123",
    "device_info": "iPhone 13 Pro"
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "user": {
            "user_id": 1,
            "phone": "13800138000",
            "nickname": "用户昵称",
            "avatar_url": "https://cdn.example.com/avatar.jpg",
            "total_points": 150,
            "available_points": 120,
            "level": 2,
            "status": "active"
        },
        "token": "sanctum-token-string"
    }
}
```

**业务逻辑**:
- 验证手机号和密码
- 记录登录日志到ct_user_logins表
- 更新用户最后登录时间和IP
- 生成新的API Token

### 3. 发送短信验证码

**端点**: `POST /auth/send-sms`

**请求参数**:
```json
{
    "phone": "13800138000",
    "type": "register"
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "验证码发送成功",
    "data": {
        "expires_in": 300
    }
}
```

### 4. 用户登出

**端点**: `POST /auth/logout`
**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "success": true,
    "message": "登出成功"
}
```

### 5. 刷新Token

**端点**: `POST /auth/refresh`
**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "success": true,
    "message": "Token刷新成功",
    "data": {
        "token": "new-sanctum-token-string"
    }
}
```

---

## 用户管理模块

### 1. 获取用户信息

**端点**: `GET /user/profile`
**请求头**: `Authorization: Bearer {token}`**响应数
据**:
```json
{
    "success": true,
    "data": {
        "user_id": 1,
        "phone": "13800138000",
        "nickname": "用户昵称",
        "real_name": "张三",
        "avatar_url": "https://cdn.example.com/avatar.jpg",
        "gender": "male",
        "birthday": "1990-01-01",
        "province": "北京市",
        "city": "北京市",
        "total_points": 150,
        "available_points": 120,
        "level": 2,
        "status": "active",
        "last_login_at": "2025-01-08T09:00:00Z",
        "created_at": "2025-01-07T10:00:00Z"
    }
}
```

### 2. 更新用户信息

**端点**: `PUT /user/profile`
**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "nickname": "新昵称",
    "real_name": "李四",
    "gender": "female",
    "birthday": "1992-05-15",
    "province": "上海市",
    "city": "上海市"
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "信息更新成功",
    "data": {
        "user_id": 1,
        "nickname": "新昵称",
        "real_name": "李四",
        "gender": "female",
        "birthday": "1992-05-15",
        "province": "上海市",
        "city": "上海市",
        "updated_at": "2025-01-08T10:00:00Z"
    }
}
```

### 3. 上传用户头像

**端点**: `POST /user/avatar`
**请求头**: 
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:
```
avatar: File (图片文件，最大1MB，支持jpg,jpeg,png,webp)
```

**响应数据**:
```json
{
    "success": true,
    "message": "头像上传成功",
    "data": {
        "avatar_url": "https://cdn.example.com/avatars/uuid.jpg",
        "file_id": 123
    }
}
```

**业务逻辑**:
- 验证文件类型和大小
- 生成UUID文件名
- 上传到指定存储位置
- 更新用户头像URL
- 记录文件信息到ct_files表
- 建立文件关联关系### 4. 
修改密码

**端点**: `PUT /user/password`
**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "current_password": "oldpassword123",
    "new_password": "newpassword123",
    "new_password_confirmation": "newpassword123"
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "密码修改成功"
}
```

### 5. 获取用户关系列表

**端点**: `GET /user/relations`
**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `type`: 关系类型 (follow, friend, block)
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "relation_id": 1,
                "user": {
                    "user_id": 2,
                    "nickname": "好友昵称",
                    "avatar_url": "https://cdn.example.com/avatar2.jpg",
                    "level": 3
                },
                "relation_type": "friend",
                "status": "accepted",
                "created_at": "2025-01-07T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 25,
            "last_page": 2
        }
    }
}
```

### 6. 关注/取消关注用户

**端点**: `POST /user/relations/{user_id}/follow`
**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "success": true,
    "message": "关注成功",
    "data": {
        "relation_id": 1,
        "relation_type": "follow",
        "status": "accepted"
    }
}
```

**业务逻辑**:
- 检查是否已存在关系
- 如果已关注则取消关注
- 如果未关注则建立关注关系
- 记录到ct_user_relations表

---

## APP管理模块

### 1. 获取APP分类列表

**端点**: `GET /apps/categories`

**查询参数**:
- `parent_id`: 父分类ID，获取子分类
- `include_inactive`: 是否包含禁用分类，默认false**
响应数据**:
```json
{
    "success": true,
    "data": [
        {
            "category_id": 1,
            "category_name": "社交通讯",
            "category_code": "social",
            "category_icon": "https://cdn.example.com/icons/social.png",
            "sort_order": 1,
            "app_count": 25
        },
        {
            "category_id": 2,
            "category_name": "金融理财",
            "category_code": "finance",
            "category_icon": "https://cdn.example.com/icons/finance.png",
            "sort_order": 2,
            "app_count": 18
        }
    ]
}
```

### 2. 获取APP列表

**端点**: `GET /apps`

**查询参数**:
- `category_id`: 分类ID
- `keyword`: 搜索关键词
- `status`: 状态筛选 (active, inactive, pending)
- `is_featured`: 是否推荐 (0, 1)
- `sort`: 排序方式 (rating, download_count, created_at)
- `order`: 排序方向 (asc, desc)
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "app_id": 1,
                "app_name": "微信",
                "app_package": "com.tencent.mm",
                "app_version": "8.0.32",
                "developer": "腾讯科技",
                "logo_url": "https://cdn.example.com/logos/wechat.png",
                "description": "微信是一款跨平台的通讯工具...",
                "rating": 4.8,
                "rating_count": 1250,
                "download_count": 50000,
                "view_count": 8500,
                "status": "active",
                "is_featured": true,
                "category": {
                    "category_id": 1,
                    "category_name": "社交通讯"
                },
                "created_at": "2025-01-01T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 100,
            "last_page": 7
        }
    }
}
```

### 3. 获取APP详情

**端点**: `GET /apps/{app_id}`*
*响应数据**:
```json
{
    "success": true,
    "data": {
        "app_id": 1,
        "app_name": "微信",
        "app_package": "com.tencent.mm",
        "app_version": "8.0.32",
        "developer": "腾讯科技",
        "app_size": 157286400,
        "download_url": "https://example.com/download/wechat.apk",
        "logo_url": "https://cdn.example.com/logos/wechat.png",
        "description": "微信是一款跨平台的通讯工具...",
        "features": [
            "即时通讯",
            "朋友圈",
            "微信支付",
            "小程序"
        ],
        "screenshots": [
            "https://cdn.example.com/screenshots/wechat1.jpg",
            "https://cdn.example.com/screenshots/wechat2.jpg"
        ],
        "rating": 4.8,
        "rating_count": 1250,
        "download_count": 50000,
        "view_count": 8500,
        "status": "active",
        "is_featured": true,
        "category": {
            "category_id": 1,
            "category_name": "社交通讯",
            "category_code": "social"
        },
        "recent_evaluations": [
            {
                "report_id": 1,
                "user": {
                    "nickname": "评测用户",
                    "avatar_url": "https://cdn.example.com/avatar.jpg"
                },
                "rating": 5,
                "report_title": "微信最新版本评测",
                "created_at": "2025-01-07T15:30:00Z"
            }
        ],
        "created_at": "2025-01-01T10:00:00Z"
    }
}
```

**业务逻辑**:
- 增加APP查看次数
- 记录用户浏览行为
- 返回最近的评测报告

### 4. 搜索APP

**端点**: `GET /apps/search`

**查询参数**:
- `q`: 搜索关键词 (必填)
- `category_id`: 分类筛选
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "keyword": "微信",
        "items": [
            {
                "app_id": 1,
                "app_name": "微信",
                "logo_url": "https://cdn.example.com/logos/wechat.png",
                "rating": 4.8,
                "download_count": 50000,
                "category_name": "社交通讯"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 5,
            "last_page": 1
        }
    }
}
```### 5. 
获取推荐APP

**端点**: `GET /apps/featured`

**查询参数**:
- `limit`: 返回数量，默认10

**响应数据**:
```json
{
    "success": true,
    "data": [
        {
            "app_id": 1,
            "app_name": "微信",
            "logo_url": "https://cdn.example.com/logos/wechat.png",
            "rating": 4.8,
            "download_count": 50000,
            "category_name": "社交通讯"
        }
    ]
}
```

---

## 评测系统模块

### 1. 提交评测报告

**端点**: `POST /evaluations`
**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "app_id": 1,
    "report_title": "微信8.0.32版本深度评测",
    "report_content": "详细的评测内容...",
    "task_description": "测试微信的各项功能",
    "completion_time": 120,
    "difficulty_level": "medium",
    "rating": 5,
    "pros": "功能丰富，界面友好",
    "cons": "占用内存较大",
    "suggestions": "优化内存使用",
    "screenshots": [
        "screenshot1.jpg",
        "screenshot2.jpg"
    ],
    "evaluation_details": [
        {
            "metric_name": "启动速度",
            "metric_value": "2.3",
            "metric_type": "number",
            "metric_unit": "秒"
        },
        {
            "metric_name": "内存占用",
            "metric_value": "156",
            "metric_type": "number",
            "metric_unit": "MB"
        }
    ]
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "评测报告提交成功",
    "data": {
        "report_id": 1,
        "status": "submitted",
        "reward_points": 10,
        "submitted_at": "2025-01-08T10:00:00Z"
    }
}
```

**业务逻辑**:
- 验证用户是否已对该APP提交过评测
- 保存评测报告到ct_evaluation_reports表
- 保存评测详细数据到ct_evaluation_details表
- 处理截图文件关联
- 发放评测奖励积分
- 提交内容审核#
## 2. 获取评测报告列表

**端点**: `GET /evaluations`

**查询参数**:
- `app_id`: APP ID筛选
- `user_id`: 用户ID筛选
- `status`: 状态筛选 (draft, submitted, approved, rejected)
- `is_featured`: 是否推荐
- `sort`: 排序方式 (created_at, rating, like_count)
- `order`: 排序方向 (asc, desc)
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "report_id": 1,
                "app": {
                    "app_id": 1,
                    "app_name": "微信",
                    "logo_url": "https://cdn.example.com/logos/wechat.png"
                },
                "user": {
                    "user_id": 1,
                    "nickname": "评测达人",
                    "avatar_url": "https://cdn.example.com/avatar.jpg",
                    "level": 3
                },
                "report_title": "微信8.0.32版本深度评测",
                "rating": 5,
                "difficulty_level": "medium",
                "reward_points": 10,
                "status": "approved",
                "view_count": 156,
                "like_count": 23,
                "is_featured": true,
                "submitted_at": "2025-01-08T10:00:00Z",
                "approved_at": "2025-01-08T11:30:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 50,
            "last_page": 4
        }
    }
}
```

### 3. 获取评测报告详情

**端点**: `GET /evaluations/{report_id}`

**响应数据**:
```json
{
    "success": true,
    "data": {
        "report_id": 1,
        "app": {
            "app_id": 1,
            "app_name": "微信",
            "logo_url": "https://cdn.example.com/logos/wechat.png",
            "category_name": "社交通讯"
        },
        "user": {
            "user_id": 1,
            "nickname": "评测达人",
            "avatar_url": "https://cdn.example.com/avatar.jpg",
            "level": 3
        },
        "report_title": "微信8.0.32版本深度评测",
        "report_content": "详细的评测内容...",
        "task_description": "测试微信的各项功能",
        "completion_time": 120,
        "difficulty_level": "medium",
        "rating": 5,
        "pros": "功能丰富，界面友好",
        "cons": "占用内存较大",
        "suggestions": "优化内存使用",
        "screenshots": [
            "https://cdn.example.com/screenshots/eval1.jpg",
            "https://cdn.example.com/screenshots/eval2.jpg"
        ],
        "evaluation_details": [
            {
                "metric_name": "启动速度",
                "metric_value": "2.3",
                "metric_type": "number",
                "metric_unit": "秒",
                "metric_description": "应用启动到主界面的时间"
            }
        ],
        "reward_points": 10,
        "status": "approved",
        "view_count": 156,
        "like_count": 23,
        "is_featured": true,
        "submitted_at": "2025-01-08T10:00:00Z",
        "approved_at": "2025-01-08T11:30:00Z"
    }
}
```

**业务逻辑**:
- 增加报告查看次数
- 记录用户浏览行为###
 4. 点赞/取消点赞评测报告

**端点**: `POST /evaluations/{report_id}/like`
**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "success": true,
    "message": "点赞成功",
    "data": {
        "is_liked": true,
        "like_count": 24
    }
}
```

### 5. 获取我的评测报告

**端点**: `GET /user/evaluations`
**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `status`: 状态筛选
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "report_id": 1,
                "app": {
                    "app_name": "微信",
                    "logo_url": "https://cdn.example.com/logos/wechat.png"
                },
                "report_title": "微信8.0.32版本深度评测",
                "rating": 5,
                "status": "approved",
                "reward_points": 10,
                "view_count": 156,
                "like_count": 23,
                "submitted_at": "2025-01-08T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 5,
            "last_page": 1
        }
    }
}
```

---

## 线索系统模块

### 1. 发布放水线索

**端点**: `POST /clues`
**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "app_id": 1,
    "clue_title": "微信红包漏洞线索",
    "clue_content": "详细的线索内容描述...",
    "clue_type": "loophole",
    "difficulty_level": "medium",
    "expected_reward": 50.00,
    "risk_level": "low",
    "steps": "操作步骤详细说明...",
    "screenshots": [
        "clue_screenshot1.jpg",
        "clue_screenshot2.jpg"
    ],
    "tags": "红包,漏洞,微信",
    "expires_at": "2025-01-15T23:59:59Z"
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "线索提交成功",
    "data": {
        "clue_id": 1,
        "status": "submitted",
        "reward_points": 30,
        "submitted_at": "2025-01-08T10:00:00Z"
    }
}
```

**业务逻辑**:
- 验证用户权限和频率限制
- 保存线索信息到ct_water_clues表
- 处理截图文件关联
- 发放线索奖励积分
- 提交内容审核### 2. 获取线
索列表

**端点**: `GET /clues`

**查询参数**:
- `app_id`: APP ID筛选
- `clue_type`: 线索类型筛选
- `difficulty_level`: 难度等级筛选
- `risk_level`: 风险等级筛选
- `status`: 状态筛选
- `is_featured`: 是否推荐
- `sort`: 排序方式 (created_at, expected_reward, success_rate)
- `order`: 排序方向 (asc, desc)
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "clue_id": 1,
                "app": {
                    "app_id": 1,
                    "app_name": "微信",
                    "logo_url": "https://cdn.example.com/logos/wechat.png"
                },
                "user": {
                    "user_id": 1,
                    "nickname": "线索达人",
                    "avatar_url": "https://cdn.example.com/avatar.jpg",
                    "level": 4
                },
                "clue_title": "微信红包漏洞线索",
                "clue_type": "loophole",
                "difficulty_level": "medium",
                "expected_reward": 50.00,
                "success_rate": 85.5,
                "risk_level": "low",
                "status": "approved",
                "view_count": 256,
                "like_count": 45,
                "try_count": 120,
                "success_count": 102,
                "is_featured": true,
                "expires_at": "2025-01-15T23:59:59Z",
                "submitted_at": "2025-01-08T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 80,
            "last_page": 6
        }
    }
}
```

### 3. 获取线索详情

**端点**: `GET /clues/{clue_id}`

**响应数据**:
```json
{
    "success": true,
    "data": {
        "clue_id": 1,
        "app": {
            "app_id": 1,
            "app_name": "微信",
            "logo_url": "https://cdn.example.com/logos/wechat.png",
            "category_name": "社交通讯"
        },
        "user": {
            "user_id": 1,
            "nickname": "线索达人",
            "avatar_url": "https://cdn.example.com/avatar.jpg",
            "level": 4
        },
        "clue_title": "微信红包漏洞线索",
        "clue_content": "详细的线索内容描述...",
        "clue_type": "loophole",
        "difficulty_level": "medium",
        "expected_reward": 50.00,
        "actual_reward": 48.50,
        "success_rate": 85.5,
        "risk_level": "low",
        "steps": "操作步骤详细说明...",
        "screenshots": [
            "https://cdn.example.com/screenshots/clue1.jpg",
            "https://cdn.example.com/screenshots/clue2.jpg"
        ],
        "tags": ["红包", "漏洞", "微信"],
        "reward_points": 30,
        "status": "approved",
        "view_count": 256,
        "like_count": 45,
        "try_count": 120,
        "success_count": 102,
        "is_featured": true,
        "expires_at": "2025-01-15T23:59:59Z",
        "submitted_at": "2025-01-08T10:00:00Z",
        "approved_at": "2025-01-08T12:00:00Z"
    }
}
```

**业务逻辑**:
- 增加线索查看次数
- 记录用户浏览行为### 4. 提
交线索反馈

**端点**: `POST /clues/{clue_id}/feedback`
**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "feedback_type": "success",
    "feedback_content": "按照步骤操作成功获得收益",
    "actual_reward": 48.50,
    "time_spent": 30,
    "difficulty_rating": 3,
    "success_rating": 4,
    "screenshots": [
        "feedback_screenshot1.jpg",
        "feedback_screenshot2.jpg"
    ]
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "反馈提交成功",
    "data": {
        "feedback_id": 1,
        "reward_points": 5,
        "submitted_at": "2025-01-08T15:00:00Z"
    }
}
```

### 5. 获取我的线索

**端点**: `GET /user/clues`
**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `status`: 状态筛选
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "clue_id": 1,
                "app": {
                    "app_name": "微信",
                    "logo_url": "https://cdn.example.com/logos/wechat.png"
                },
                "clue_title": "微信红包漏洞线索",
                "expected_reward": 50.00,
                "status": "approved",
                "reward_points": 30,
                "view_count": 256,
                "try_count": 120,
                "success_count": 102,
                "submitted_at": "2025-01-08T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 8,
            "last_page": 1
        }
    }
}
```

---

## 积分系统模块

### 1. 获取积分记录

**端点**: `GET /user/points`
**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `change_type`: 变化类型筛选 (earn, spend, expire, adjust)
- `source_type`: 来源类型筛选
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15

**响应数据**:
```json
{
    "success": true,
    "data": {
        "summary": {
            "total_points": 150,
            "available_points": 120,
            "used_points": 30,
            "expired_points": 0
        },
        "items": [
            {
                "record_id": 1,
                "point_change": 50,
                "point_balance": 150,
                "change_type": "earn",
                "source_type": "evaluation_report",
                "source_id": 1,
                "description": "提交评测报告奖励",
                "created_at": "2025-01-08T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 25,
            "last_page": 2
        }
    }
}
```### 2. 
获取积分规则

**端点**: `GET /points/rules`

**响应数据**:
```json
{
    "success": true,
    "data": [
        {
            "rule_id": 1,
            "rule_name": "注册奖励",
            "rule_code": "register_reward",
            "action_type": "register",
            "point_value": 50,
            "daily_limit": 1,
            "description": "新用户注册奖励50积分",
            "is_active": true
        },
        {
            "rule_id": 2,
            "rule_name": "评测报告奖励",
            "rule_code": "evaluation_reward",
            "action_type": "submit_evaluation",
            "point_value": 10,
            "daily_limit": 3,
            "description": "提交评测报告奖励10积分，每日最多3次",
            "is_active": true
        }
    ]
}
```

### 3. 获取奖励配置

**端点**: `GET /points/rewards`

**查询参数**:
- `config_type`: 配置类型筛选
- `reward_type`: 奖励类型筛选

**响应数据**:
```json
{
    "success": true,
    "data": [
        {
            "config_id": 1,
            "config_name": "话费充值10元",
            "config_type": "point_exchange",
            "reward_type": "gift",
            "reward_value": {
                "type": "phone_credit",
                "amount": 10,
                "description": "手机话费充值10元"
            },
            "required_points": 100,
            "stock_quantity": 50,
            "used_quantity": 5,
            "is_active": true
        }
    ]
}
```

### 4. 兑换奖励

**端点**: `POST /points/exchange`
**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "config_id": 1,
    "quantity": 1,
    "contact_info": {
        "phone": "13800138000",
        "address": "北京市朝阳区xxx"
    }
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "兑换成功",
    "data": {
        "exchange_id": 1,
        "points_used": 100,
        "remaining_points": 50,
        "status": "processing",
        "estimated_delivery": "2025-01-10T00:00:00Z"
    }
}
```

---

## 文件管理模块

### 1. 上传文件

**端点**: `POST /files/upload`
**请求头**: 
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:
```
file: File (文件)
category_code: string (文件分类代码)
business_type: string (业务类型，可选)
business_id: integer (业务ID，可选)
```

**响应数据**:
```json
{
    "success": true,
    "message": "文件上传成功",
    "data": {
        "file_id": 123,
        "original_name": "screenshot.jpg",
        "file_url": "https://cdn.example.com/files/uuid.jpg",
        "file_size": 1024000,
        "file_type": "image/jpeg",
        "width": 1920,
        "height": 1080
    }
}
```### 
2. 批量上传文件

**端点**: `POST /files/batch-upload`
**请求头**: 
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:
```
files[]: File[] (文件数组)
category_code: string (文件分类代码)
business_type: string (业务类型，可选)
business_id: integer (业务ID，可选)
```

**响应数据**:
```json
{
    "success": true,
    "message": "文件批量上传成功",
    "data": {
        "uploaded_files": [
            {
                "file_id": 123,
                "original_name": "screenshot1.jpg",
                "file_url": "https://cdn.example.com/files/uuid1.jpg",
                "file_size": 1024000
            },
            {
                "file_id": 124,
                "original_name": "screenshot2.jpg",
                "file_url": "https://cdn.example.com/files/uuid2.jpg",
                "file_size": 2048000
            }
        ],
        "failed_files": []
    }
}
```

### 3. 获取文件信息

**端点**: `GET /files/{file_id}`

**响应数据**:
```json
{
    "success": true,
    "data": {
        "file_id": 123,
        "original_name": "screenshot.jpg",
        "stored_name": "uuid.jpg",
        "file_url": "https://cdn.example.com/files/uuid.jpg",
        "file_size": 1024000,
        "file_type": "image/jpeg",
        "file_extension": "jpg",
        "width": 1920,
        "height": 1080,
        "storage_type": "local",
        "is_public": true,
        "uploader": {
            "user_id": 1,
            "nickname": "用户昵称"
        },
        "created_at": "2025-01-08T10:00:00Z"
    }
}
```

### 4. 删除文件

**端点**: `DELETE /files/{file_id}`
**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "success": true,
    "message": "文件删除成功"
}
```

### 5. 获取文件分类

**端点**: `GET /files/categories`

**响应数据**:
```json
{
    "success": true,
    "data": [
        {
            "category_id": 1,
            "category_name": "用户头像",
            "category_code": "avatar",
            "max_file_size": 1048576,
            "allowed_extensions": ["jpg", "jpeg", "png", "webp"],
            "description": "用户头像图片"
        },
        {
            "category_id": 2,
            "category_name": "评测截图",
            "category_code": "evaluation_screenshot",
            "max_file_size": 5242880,
            "allowed_extensions": ["jpg", "jpeg", "png"],
            "description": "评测报告截图"
        }
    ]
}
```

---

## 内容审核模块

### 1. 获取待审核内容

**端点**: `GET /admin/audits/pending`
**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `content_type`: 内容类型筛选
- `priority_level`: 优先级筛选
- `page`: 页码，默认1
- `per_page`: 每页数量，默认15**
响应数据**:
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "audit_id": 1,
                "content_type": "evaluation_report",
                "content_id": 1,
                "submitter": {
                    "user_id": 1,
                    "nickname": "用户昵称",
                    "avatar_url": "https://cdn.example.com/avatar.jpg"
                },
                "content_snapshot": {
                    "title": "微信评测报告",
                    "content": "详细内容..."
                },
                "auto_audit_score": 75,
                "auto_audit_result": "manual",
                "priority_level": "normal",
                "submitted_at": "2025-01-08T10:00:00Z",
                "timeout_at": "2025-01-11T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 25,
            "last_page": 2
        }
    }
}
```

### 2. 审核内容

**端点**: `POST /admin/audits/{audit_id}/review`
**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "audit_result": "pass",
    "audit_reason": "内容符合规范，通过审核",
    "audit_score": 90
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "审核完成",
    "data": {
        "audit_id": 1,
        "audit_result": "pass",
        "audit_completed_at": "2025-01-08T15:00:00Z"
    }
}
```

### 3. 获取审核统计

**端点**: `GET /admin/audits/statistics`
**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `content_type`: 内容类型

**响应数据**:
```json
{
    "success": true,
    "data": {
        "total_audits": 100,
        "pending_audits": 15,
        "passed_audits": 70,
        "rejected_audits": 15,
        "timeout_audits": 0,
        "avg_audit_time": 2.5,
        "by_content_type": {
            "evaluation_report": {
                "total": 60,
                "passed": 45,
                "rejected": 15
            },
            "water_clue": {
                "total": 40,
                "passed": 25,
                "rejected": 15
            }
        }
    }
}
```

---

## 系统配置模块

### 1. 获取系统配置

**端点**: `GET /system/configs`

**查询参数**:
- `config_group`: 配置分组
- `is_public`: 是否公开配置

**响应数据**:
```json
{
    "success": true,
    "data": [
        {
            "config_key": "app_name",
            "config_name": "应用名称",
            "config_value": "次推",
            "config_type": "string",
            "config_group": "basic"
        },
        {
            "config_key": "point_exchange_rate",
            "config_name": "积分兑换比例",
            "config_value": "100",
            "config_type": "number",
            "config_group": "points"
        }
    ]
}
```### 
2. 更新系统配置

**端点**: `PUT /admin/system/configs`
**请求头**: `Authorization: Bearer {admin_token}`

**请求参数**:
```json
{
    "configs": [
        {
            "config_key": "point_exchange_rate",
            "config_value": "120"
        },
        {
            "config_key": "max_daily_evaluations",
            "config_value": "5"
        }
    ]
}
```

**响应数据**:
```json
{
    "success": true,
    "message": "配置更新成功",
    "data": {
        "updated_count": 2,
        "updated_configs": [
            "point_exchange_rate",
            "max_daily_evaluations"
        ]
    }
}
```

---

## 数据统计模块

### 1. 获取用户统计

**端点**: `GET /admin/statistics/users`
**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `group_by`: 分组方式 (day, week, month)

**响应数据**:
```json
{
    "success": true,
    "data": {
        "summary": {
            "total_users": 1000,
            "active_users": 800,
            "new_users_today": 25,
            "new_users_this_month": 150
        },
        "chart_data": [
            {
                "date": "2025-01-01",
                "new_users": 10,
                "active_users": 50
            },
            {
                "date": "2025-01-02",
                "new_users": 15,
                "active_users": 65
            }
        ]
    }
}
```

### 2. 获取内容统计

**端点**: `GET /admin/statistics/content`
**请求头**: `Authorization: Bearer {admin_token}`

**查询参数**:
- `content_type`: 内容类型 (evaluation_report, water_clue)
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应数据**:
```json
{
    "success": true,
    "data": {
        "evaluation_reports": {
            "total": 500,
            "approved": 400,
            "pending": 50,
            "rejected": 50,
            "today_submitted": 15
        },
        "water_clues": {
            "total": 300,
            "approved": 250,
            "pending": 30,
            "rejected": 20,
            "today_submitted": 8
        }
    }
}
```

### 3. 获取积分统计

**端点**: `GET /admin/statistics/points`
**请求头**: `Authorization: Bearer {admin_token}`

**响应数据**:
```json
{
    "success": true,
    "data": {
        "total_points_issued": 50000,
        "total_points_used": 15000,
        "points_by_source": {
            "register_reward": 5000,
            "evaluation_reward": 30000,
            "clue_reward": 15000
        },
        "top_earners": [
            {
                "user_id": 1,
                "nickname": "积分达人",
                "total_points": 500
            }
        ]
    }
}
```

---

## 错误处理规范

### 错误响应格式

```json
{
    "success": false,
    "message": "错误描述",
    "error_code": "ERROR_CODE",
    "errors": {
        "field_name": ["具体错误信息"]
    },
    "meta": {
        "timestamp": "2025-01-08T10:00:00Z",
        "request_id": "uuid-string"
    }
}
```### 常
见错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| VALIDATION_ERROR | 422 | 数据验证失败 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| DUPLICATE_ENTRY | 409 | 数据重复 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INSUFFICIENT_POINTS | 400 | 积分不足 |
| FILE_TOO_LARGE | 413 | 文件过大 |
| INVALID_FILE_TYPE | 400 | 文件类型不支持 |
| AUDIT_PENDING | 400 | 内容审核中 |

### 业务逻辑错误处理

1. **用户相关错误**
   - 手机号已注册: `PHONE_ALREADY_EXISTS`
   - 验证码错误: `INVALID_VERIFICATION_CODE`
   - 密码错误: `INVALID_PASSWORD`

2. **内容提交错误**
   - 重复提交: `DUPLICATE_SUBMISSION`
   - 内容违规: `CONTENT_VIOLATION`
   - 审核超时: `AUDIT_TIMEOUT`

3. **积分系统错误**
   - 积分不足: `INSUFFICIENT_POINTS`
   - 兑换库存不足: `INSUFFICIENT_STOCK`
   - 超出每日限制: `DAILY_LIMIT_EXCEEDED`

4. **文件上传错误**
   - 文件过大: `FILE_TOO_LARGE`
   - 文件类型不支持: `UNSUPPORTED_FILE_TYPE`
   - 存储空间不足: `STORAGE_FULL`

---

## 数据验证规则

### 用户数据验证

```php
// 用户注册验证
'phone' => 'required|regex:/^1[3-9]\d{9}$/|unique:ct_users,phone',
'password' => 'required|min:6|max:20|confirmed',
'nickname' => 'required|string|min:2|max:20|unique:ct_users,nickname',
'verification_code' => 'required|digits:6'

// 用户信息更新验证
'nickname' => 'sometimes|string|min:2|max:20|unique:ct_users,nickname,' . $userId,
'real_name' => 'sometimes|string|max:50',
'gender' => 'sometimes|in:male,female,unknown',
'birthday' => 'sometimes|date|before:today',
'province' => 'sometimes|string|max:50',
'city' => 'sometimes|string|max:50'
```

### APP数据验证

```php
// APP信息验证
'app_name' => 'required|string|max:200',
'app_package' => 'sometimes|string|max:200',
'app_version' => 'sometimes|string|max:50',
'developer' => 'sometimes|string|max:200',
'category_id' => 'required|exists:ct_app_categories,category_id',
'description' => 'sometimes|string|max:2000',
'rating' => 'sometimes|numeric|between:0,5'
```

### 评测报告验证

```php
// 评测报告验证
'app_id' => 'required|exists:ct_apps,app_id',
'report_title' => 'required|string|min:5|max:200',
'report_content' => 'required|string|min:50|max:5000',
'task_description' => 'sometimes|string|max:1000',
'completion_time' => 'sometimes|integer|min:1|max:1440',
'difficulty_level' => 'required|in:easy,medium,hard',
'rating' => 'required|integer|between:1,5',
'pros' => 'sometimes|string|max:500',
'cons' => 'sometimes|string|max:500',
'suggestions' => 'sometimes|string|max:500',
'screenshots' => 'sometimes|array|max:5',
'screenshots.*' => 'string|max:255'
```### 线索数据验证


```php
// 放水线索验证
'app_id' => 'required|exists:ct_apps,app_id',
'clue_title' => 'required|string|min:5|max:200',
'clue_content' => 'required|string|min:20|max:3000',
'clue_type' => 'required|in:bug,loophole,promotion,other',
'difficulty_level' => 'required|in:easy,medium,hard',
'expected_reward' => 'sometimes|numeric|between:0,9999.99',
'risk_level' => 'required|in:low,medium,high',
'steps' => 'sometimes|string|max:2000',
'screenshots' => 'sometimes|array|max:3',
'screenshots.*' => 'string|max:255',
'tags' => 'sometimes|string|max:500',
'expires_at' => 'sometimes|date|after:now'
```

### 文件上传验证

```php
// 文件上传验证
'file' => 'required|file|max:10240', // 最大10MB
'category_code' => 'required|exists:ct_file_categories,category_code',
'business_type' => 'sometimes|string|max:50',
'business_id' => 'sometimes|integer|min:1'

// 图片文件验证
'avatar' => 'required|image|mimes:jpg,jpeg,png,webp|max:1024', // 最大1MB
'screenshot' => 'required|image|mimes:jpg,jpeg,png|max:5120' // 最大5MB
```

### 积分兑换验证

```php
// 积分兑换验证
'config_id' => 'required|exists:ct_reward_configs,config_id',
'quantity' => 'required|integer|min:1|max:10',
'contact_info' => 'required|array',
'contact_info.phone' => 'required|regex:/^1[3-9]\d{9}$/',
'contact_info.address' => 'sometimes|string|max:200'
```

### 审核数据验证

```php
// 内容审核验证
'audit_result' => 'required|in:pass,reject',
'audit_reason' => 'required|string|min:5|max:500',
'audit_score' => 'sometimes|integer|between:0,100',
'reject_category' => 'required_if:audit_result,reject|in:spam,inappropriate,fake,duplicate'
```

---

## 实现要点和注意事项

### 1. 数据库设计要点

- 所有表使用`utf8mb4`字符集支持emoji
- 主键统一使用`BIGINT AUTO_INCREMENT`
- 时间字段统一使用`TIMESTAMP`类型
- 软删除使用`deleted_at`字段
- 重要字段添加索引优化查询性能
- JSON字段用于存储灵活的结构化数据

### 2. API设计原则

- RESTful API设计风格
- 统一的响应格式和错误处理
- 合理的HTTP状态码使用
- 支持分页和排序
- 请求频率限制和防刷机制
- API版本控制支持

### 3. 安全性要求

- 所有敏感接口需要Token认证
- 密码使用bcrypt加密存储
- 文件上传类型和大小限制
- SQL注入和XSS攻击防护
- 敏感操作记录审计日志
- 数据访问权限控制

### 4. 性能优化

- 数据库查询优化和索引设计
- Redis缓存热点数据
- 文件上传支持云存储
- 大数据量接口分页处理
- 异步队列处理耗时任务
- CDN加速静态资源访问

### 5. 业务逻辑要点

- 积分系统的事务一致性
- 内容审核的状态流转
- 文件关联的多态设计
- 用户权限的分级管理
- 数据统计的实时性和准确性
- 系统配置的热更新机制

### 6. 开发规范

- 使用Laravel的Eloquent ORM
- 遵循PSR编码规范
- 完善的异常处理机制
- 单元测试和集成测试
- API文档自动生成
- 代码注释和文档完善

---

## 总结

本文档提供了次推应用Laravel 9后端API的完整实现需求，包括：

1. **23个数据库表**的完整结构设计
2. **80+个API接口**的详细规格说明
3. **9个核心业务模块**的功能实现要求
4. **完整的数据验证规则**和错误处理规范
5. **安全性和性能优化**的实现要点

开发人员可以基于此文档独立完成整个后端系统的开发工作，无需额外的需求澄清或技术咨询。

文档涵盖了从用户认证、内容管理、积分系统到数据统计的完整业务流程，确保了系统的完整性和可扩展性。