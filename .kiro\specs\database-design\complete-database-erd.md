# 次推应用完整数据库实体关系图

## 完整系统ERD图

```mermaid
erDiagram
    %% 第一层：基础表
    ct_admin_users {
        bigint admin_id PK "管理员ID"
        varchar username UK "用户名"
        varchar password_hash "密码哈希"
        varchar real_name "真实姓名"
        varchar email UK "邮箱"
        varchar phone UK "手机号"
        varchar role "角色"
        json permissions "权限列表"
        tinyint is_active "是否启用"
        timestamp created_at "创建时间"
    }

    ct_users {
        bigint user_id PK "用户ID"
        varchar phone UK "手机号"
        varchar nickname "昵称"
        varchar real_name "真实姓名"
        int total_points "总积分"
        int available_points "可用积分"
        int level "用户等级"
        enum status "用户状态"
        timestamp created_at "创建时间"
    }

    ct_app_categories {
        int category_id PK "分类ID"
        varchar category_name "分类名称"
        varchar category_code UK "分类代码"
        int parent_id FK "父分类ID"
        int sort_order "排序顺序"
        tinyint is_active "是否启用"
        timestamp created_at "创建时间"
    }

    ct_file_categories {
        int category_id PK "分类ID"
        varchar category_name "分类名称"
        varchar category_code UK "分类代码"
        int parent_id FK "父分类ID"
        bigint max_file_size "最大文件大小"
        varchar allowed_extensions "允许扩展名"
        tinyint is_active "是否启用"
        timestamp created_at "创建时间"
    }

    %% 第二层：依赖基础表
    ct_user_logins {
        bigint login_id PK "登录ID"
        bigint user_id FK "用户ID"
        enum login_type "登录方式"
        varchar login_ip "登录IP"
        enum login_status "登录状态"
        varchar session_id "会话ID"
        timestamp created_at "登录时间"
    }

    ct_user_relations {
        bigint relation_id PK "关系ID"
        bigint user_id FK "用户ID"
        bigint related_user_id FK "关联用户ID"
        enum relation_type "关系类型"
        enum status "关系状态"
        timestamp created_at "创建时间"
    }

    ct_apps {
        bigint app_id PK "应用ID"
        int category_id FK "分类ID"
        varchar app_name "应用名称"
        varchar app_package "应用包名"
        varchar logo_url "Logo URL"
        text description "应用描述"
        decimal rating "平均评分"
        bigint download_count "下载次数"
        enum status "应用状态"
        tinyint is_featured "是否推荐"
        timestamp created_at "创建时间"
    }

    ct_files {
        bigint file_id PK "文件ID"
        int category_id FK "文件分类ID"
        bigint uploader_id FK "上传用户ID"
        varchar original_name "原始文件名"
        varchar file_path "文件路径"
        varchar file_url "文件URL"
        bigint file_size "文件大小"
        varchar file_hash "文件哈希"
        enum storage_type "存储类型"
        tinyint is_deleted "是否删除"
        timestamp created_at "创建时间"
    }

    ct_point_rules {
        int rule_id PK "规则ID"
        varchar rule_name "规则名称"
        varchar rule_code UK "规则代码"
        varchar action_type "动作类型"
        int point_value "积分值"
        int daily_limit "每日限制"
        tinyint is_active "是否启用"
        timestamp created_at "创建时间"
    }

    ct_reward_configs {
        int config_id PK "配置ID"
        varchar config_name "配置名称"
        enum config_type "配置类型"
        enum reward_type "奖励类型"
        json reward_value "奖励内容"
        int required_points "所需积分"
        tinyint is_active "是否启用"
        timestamp created_at "创建时间"
    }

    ct_system_configs {
        int config_id PK "配置ID"
        varchar config_key UK "配置键名"
        varchar config_name "配置名称"
        text config_value "配置值"
        enum config_type "配置类型"
        varchar config_group "配置分组"
        tinyint is_active "是否启用"
        int version "版本号"
        bigint created_by FK "创建人"
        timestamp created_at "创建时间"
    }

    ct_audit_rules {
        int rule_id PK "规则ID"
        varchar rule_name "规则名称"
        varchar rule_code UK "规则代码"
        varchar content_type "内容类型"
        tinyint auto_audit_enabled "启用自动审核"
        int audit_timeout_hours "超时时间"
        int reward_points "奖励积分"
        tinyint is_active "是否启用"
        bigint created_by FK "创建人"
        timestamp created_at "创建时间"
    }

    %% 第三层：业务表
    ct_evaluation_reports {
        bigint report_id PK "报告ID"
        bigint app_id FK "应用ID"
        bigint user_id FK "用户ID"
        varchar report_title "报告标题"
        text report_content "报告内容"
        tinyint rating "评分"
        json screenshots "截图列表"
        int reward_points "奖励积分"
        enum status "状态"
        tinyint is_featured "是否推荐"
        timestamp created_at "创建时间"
    }

    ct_evaluation_details {
        bigint detail_id PK "详情ID"
        bigint report_id FK "报告ID"
        varchar metric_name "指标名称"
        varchar metric_value "指标值"
        enum metric_type "指标类型"
        int sort_order "排序"
        timestamp created_at "创建时间"
    }

    ct_water_clues {
        bigint clue_id PK "线索ID"
        bigint app_id FK "应用ID"
        bigint user_id FK "用户ID"
        varchar clue_title "线索标题"
        text clue_content "线索内容"
        enum clue_type "线索类型"
        decimal expected_reward "预期收益"
        decimal success_rate "成功率"
        enum risk_level "风险等级"
        int reward_points "奖励积分"
        enum status "状态"
        timestamp expires_at "过期时间"
        timestamp created_at "创建时间"
    }

    ct_clue_feedbacks {
        bigint feedback_id PK "反馈ID"
        bigint clue_id FK "线索ID"
        bigint user_id FK "用户ID"
        enum feedback_type "反馈类型"
        text feedback_content "反馈内容"
        decimal actual_reward "实际收益"
        tinyint success_rating "成功评分"
        tinyint is_verified "是否验证"
        timestamp created_at "创建时间"
    }

    ct_clue_statistics {
        bigint stat_id PK "统计ID"
        bigint clue_id FK "线索ID"
        date stat_date "统计日期"
        int try_count "尝试次数"
        int success_count "成功次数"
        decimal total_reward "总收益"
        decimal success_rate "成功率"
        timestamp created_at "创建时间"
    }

    ct_point_records {
        bigint record_id PK "记录ID"
        bigint user_id FK "用户ID"
        int rule_id FK "规则ID"
        int point_change "积分变化"
        int point_balance "积分余额"
        enum change_type "变化类型"
        varchar source_type "来源类型"
        bigint source_id "来源ID"
        varchar description "描述"
        timestamp created_at "创建时间"
    }

    ct_file_relations {
        bigint relation_id PK "关联ID"
        bigint file_id FK "文件ID"
        varchar business_type "业务类型"
        bigint business_id "业务对象ID"
        varchar relation_type "关联类型"
        tinyint is_primary "是否主要"
        timestamp created_at "创建时间"
    }

    ct_content_audits {
        bigint audit_id PK "审核ID"
        int rule_id FK "规则ID"
        varchar content_type "内容类型"
        bigint content_id "内容ID"
        bigint submitter_id FK "提交者ID"
        enum audit_status "审核状态"
        bigint auditor_id FK "审核员ID"
        enum audit_result "审核结果"
        text audit_reason "审核原因"
        json content_snapshot "内容快照"
        timestamp submitted_at "提交时间"
        timestamp created_at "创建时间"
    }

    ct_audit_logs {
        bigint log_id PK "日志ID"
        bigint audit_id FK "审核ID"
        bigint operator_id FK "操作人ID"
        enum operator_type "操作人类型"
        varchar action_type "操作类型"
        varchar old_status "原状态"
        varchar new_status "新状态"
        text action_reason "操作原因"
        timestamp created_at "创建时间"
    }

    ct_operation_logs {
        bigint log_id PK "日志ID"
        bigint operator_id FK "操作人ID"
        enum operator_type "操作人类型"
        varchar module "操作模块"
        varchar action "操作动作"
        varchar resource_type "资源类型"
        bigint resource_id "资源ID"
        text operation_desc "操作描述"
        enum operation_result "操作结果"
        varchar ip_address "操作IP"
        timestamp created_at "创建时间"
    }

    ct_statistics {
        bigint stat_id PK "统计ID"
        varchar stat_key "统计键名"
        varchar stat_category "统计分类"
        enum stat_type "统计类型"
        decimal stat_value "统计值"
        bigint stat_count "统计次数"
        enum time_period "时间周期"
        date stat_date "统计日期"
        json stat_data "扩展数据"
        timestamp created_at "创建时间"
    }

    %% 关系定义
    %% 第一层自关联
    ct_app_categories ||--o{ ct_app_categories : "parent_id"
    ct_file_categories ||--o{ ct_file_categories : "parent_id"

    %% 第一层到第二层
    ct_users ||--o{ ct_user_logins : "user_id"
    ct_users ||--o{ ct_user_relations : "user_id"
    ct_users ||--o{ ct_user_relations : "related_user_id"
    ct_app_categories ||--o{ ct_apps : "category_id"
    ct_file_categories ||--o{ ct_files : "category_id"
    ct_users ||--o{ ct_files : "uploader_id"
    ct_admin_users ||--o{ ct_system_configs : "created_by"
    ct_admin_users ||--o{ ct_audit_rules : "created_by"

    %% 第二层到第三层
    ct_apps ||--o{ ct_evaluation_reports : "app_id"
    ct_users ||--o{ ct_evaluation_reports : "user_id"
    ct_evaluation_reports ||--o{ ct_evaluation_details : "report_id"
    ct_apps ||--o{ ct_water_clues : "app_id"
    ct_users ||--o{ ct_water_clues : "user_id"
    ct_water_clues ||--o{ ct_clue_feedbacks : "clue_id"
    ct_users ||--o{ ct_clue_feedbacks : "user_id"
    ct_water_clues ||--o{ ct_clue_statistics : "clue_id"
    ct_users ||--o{ ct_point_records : "user_id"
    ct_point_rules ||--o{ ct_point_records : "rule_id"
    ct_files ||--o{ ct_file_relations : "file_id"
    ct_audit_rules ||--o{ ct_content_audits : "rule_id"
    ct_users ||--o{ ct_content_audits : "submitter_id"
    ct_admin_users ||--o{ ct_content_audits : "auditor_id"
    ct_content_audits ||--o{ ct_audit_logs : "audit_id"
    ct_admin_users ||--o{ ct_audit_logs : "operator_id"
    ct_users ||--o{ ct_operation_logs : "operator_id"
```

## 分层架构关系图

```mermaid
graph TD
    subgraph "第一层：基础表"
        A1[ct_admin_users<br/>管理员用户表]
        A2[ct_users<br/>普通用户表]
        A3[ct_app_categories<br/>APP分类表]
        A4[ct_file_categories<br/>文件分类表]
    end

    subgraph "第二层：依赖基础表"
        B1[ct_user_logins<br/>用户登录记录表]
        B2[ct_user_relations<br/>用户关系表]
        B3[ct_apps<br/>APP信息表]
        B4[ct_files<br/>文件信息表]
        B5[ct_point_rules<br/>积分规则表]
        B6[ct_reward_configs<br/>奖励配置表]
        B7[ct_system_configs<br/>系统配置表]
        B8[ct_audit_rules<br/>审核规则表]
    end

    subgraph "第三层：业务表"
        C1[ct_evaluation_reports<br/>评测报告表]
        C2[ct_evaluation_details<br/>评测数据详情表]
        C3[ct_water_clues<br/>放水线索表]
        C4[ct_clue_feedbacks<br/>线索反馈表]
        C5[ct_clue_statistics<br/>线索统计表]
        C6[ct_point_records<br/>积分记录表]
        C7[ct_file_relations<br/>文件关联表]
        C8[ct_content_audits<br/>内容审核表]
        C9[ct_audit_logs<br/>审核日志表]
        C10[ct_operation_logs<br/>操作日志表]
        C11[ct_statistics<br/>统计数据表]
    end

    %% 第一层到第二层的依赖
    A2 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    A2 --> B4
    A1 --> B7
    A1 --> B8

    %% 第二层到第三层的依赖
    B3 --> C1
    A2 --> C1
    C1 --> C2
    B3 --> C3
    A2 --> C3
    C3 --> C4
    A2 --> C4
    C3 --> C5
    A2 --> C6
    B5 --> C6
    B4 --> C7
    B8 --> C8
    A2 --> C8
    A1 --> C8
    C8 --> C9
    A1 --> C9
    A2 --> C10

    %% 样式定义
    classDef layer1 fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef layer2 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef layer3 fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class A1,A2,A3,A4 layer1
    class B1,B2,B3,B4,B5,B6,B7,B8 layer2
    class C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11 layer3
```

## 业务模块关系图

```mermaid
graph LR
    subgraph "用户管理模块"
        U1[ct_users]
        U2[ct_admin_users]
        U3[ct_user_logins]
        U4[ct_user_relations]
    end

    subgraph "APP管理模块"
        A1[ct_app_categories]
        A2[ct_apps]
    end

    subgraph "评测系统模块"
        E1[ct_evaluation_reports]
        E2[ct_evaluation_details]
    end

    subgraph "线索系统模块"
        W1[ct_water_clues]
        W2[ct_clue_feedbacks]
        W3[ct_clue_statistics]
    end

    subgraph "积分系统模块"
        P1[ct_point_rules]
        P2[ct_reward_configs]
        P3[ct_point_records]
    end

    subgraph "文件管理模块"
        F1[ct_file_categories]
        F2[ct_files]
        F3[ct_file_relations]
    end

    subgraph "审核系统模块"
        C1[ct_audit_rules]
        C2[ct_content_audits]
        C3[ct_audit_logs]
    end

    subgraph "系统管理模块"
        S1[ct_system_configs]
        S2[ct_operation_logs]
        S3[ct_statistics]
    end

    %% 模块间关系
    U1 -.-> E1
    U1 -.-> W1
    U1 -.-> P3
    U1 -.-> F2
    U1 -.-> C2
    U2 -.-> S1
    U2 -.-> C1
    U2 -.-> C2
    A2 -.-> E1
    A2 -.-> W1
    E1 -.-> E2
    W1 -.-> W2
    W1 -.-> W3
    P1 -.-> P3
    F2 -.-> F3
    C1 -.-> C2
    C2 -.-> C3
```

## 核心数据流图

```mermaid
flowchart TD
    Start([用户注册登录]) --> UserMgmt[用户管理系统]
    UserMgmt --> AppBrowse[浏览APP]
    AppBrowse --> Evaluation[提交评测报告]
    AppBrowse --> Clue[分享放水线索]
    
    Evaluation --> ContentAudit[内容审核]
    Clue --> ContentAudit
    
    ContentAudit --> Approved{审核通过?}
    Approved -->|是| PointReward[积分奖励]
    Approved -->|否| Rejected[审核拒绝]
    
    PointReward --> PointSystem[积分系统]
    PointSystem --> RewardExchange[奖励兑换]
    
    Evaluation --> FileUpload[文件上传]
    Clue --> FileUpload
    FileUpload --> FileManagement[文件管理]
    
    UserMgmt --> Statistics[数据统计]
    Evaluation --> Statistics
    Clue --> Statistics
    PointSystem --> Statistics
    
    ContentAudit --> AuditLog[审核日志]
    UserMgmt --> OperationLog[操作日志]
    
    SystemConfig[系统配置] --> UserMgmt
    SystemConfig --> ContentAudit
    SystemConfig --> PointSystem
```

## 表关联强度图

```mermaid
graph TD
    ct_users[ct_users<br/>用户表<br/>🔥🔥🔥🔥🔥]
    ct_apps[ct_apps<br/>APP表<br/>🔥🔥🔥🔥]
    ct_files[ct_files<br/>文件表<br/>🔥🔥🔥]
    ct_content_audits[ct_content_audits<br/>审核表<br/>🔥🔥🔥]
    ct_admin_users[ct_admin_users<br/>管理员表<br/>🔥🔥]
    
    ct_users ---|强关联| ct_evaluation_reports
    ct_users ---|强关联| ct_water_clues
    ct_users ---|强关联| ct_point_records
    ct_users ---|中关联| ct_user_logins
    ct_users ---|中关联| ct_files
    
    ct_apps ---|强关联| ct_evaluation_reports
    ct_apps ---|强关联| ct_water_clues
    ct_apps ---|弱关联| ct_app_categories
    
    ct_files ---|中关联| ct_file_relations
    ct_files ---|弱关联| ct_file_categories
    
    ct_content_audits ---|中关联| ct_audit_logs
    ct_content_audits ---|弱关联| ct_audit_rules
    
    ct_admin_users ---|弱关联| ct_system_configs
    ct_admin_users ---|弱关联| ct_audit_rules
    ct_admin_users ---|中关联| ct_content_audits
```

## 索引分布图

```mermaid
pie title 索引类型分布
    "主键索引" : 23
    "外键索引" : 45
    "唯一索引" : 15
    "单列索引" : 120
    "复合索引" : 50
```

## 数据量预估图

```mermaid
graph LR
    subgraph "大数据量表 (百万级)"
        L1[ct_operation_logs<br/>操作日志]
        L2[ct_point_records<br/>积分记录]
        L3[ct_user_logins<br/>登录记录]
        L4[ct_statistics<br/>统计数据]
    end
    
    subgraph "中数据量表 (十万级)"
        M1[ct_evaluation_reports<br/>评测报告]
        M2[ct_water_clues<br/>放水线索]
        M3[ct_files<br/>文件信息]
        M4[ct_content_audits<br/>内容审核]
    end
    
    subgraph "小数据量表 (万级)"
        S1[ct_users<br/>用户表]
        S2[ct_apps<br/>APP表]
        S3[ct_clue_feedbacks<br/>线索反馈]
        S4[ct_audit_logs<br/>审核日志]
    end
    
    subgraph "配置表 (千级)"
        C1[ct_system_configs<br/>系统配置]
        C2[ct_point_rules<br/>积分规则]
        C3[ct_app_categories<br/>APP分类]
        C4[ct_admin_users<br/>管理员]
    end
```

---

## 总结

次推应用数据库采用分层架构设计，共23个核心表，通过合理的依赖关系和索引策略，构建了一个高性能、高可靠性的数据库系统。设计充分考虑了业务需求、性能优化、安全性和扩展性，能够支撑应用的长期发展需要。