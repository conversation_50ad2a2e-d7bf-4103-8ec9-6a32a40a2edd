<?php

namespace Tests\Unit\Service\User;

use Tests\TestCase;
use App\Models\User\User as UserModel;
use App\Models\System\AdvertiserModel;
use App\Models\User\RedPacket as RedPacketModel;
use App\Models\System\AdvertisementModel;
use App\Service\User\UserService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UserServiceTest extends TestCase
{

    protected $userService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userService = new UserService();
        $logpath = storage_path("logs/laravel-".date('Y-m-d').".log");
        //清理旧的测试日志
        if(file_exists($logpath)) {
            unlink($logpath);
        } 

    }

    /**
     * 记录测试日志
     */
    protected function log($message, $data = [])
    {
        return '';
        $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message;
        if (!empty($data)) {
            $logMessage .= "\nData: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }
        Log::channel('daily')->info($logMessage);
    }

    /**
     * 准备测试数据
     */
    protected function prepareTestData($params = [])
    {
        $this->log("开始准备测试数据");

        // 创建广告商 - 先检查是否存在
        $advertiser = AdvertiserModel::firstOrCreate(
            ['as' => 'test_advertiser'],
            [
                'title' => 'Test Advertiser',
                'create_time' => time(),
                'update_time' => time()
            ]
        );
        $this->log("创建测试广告商", $advertiser->toArray());

        // 创建用户 - 先检查是否存在
        $userData = array_merge([
            'phone' => '***********',
            'pwd' => md5('123456'),
            'invite_code' => 'TEST01',
            'identity' => 2,
            'commission' => 0,
            'user_commission' => 0,
            'admin_commission' => 0,
            'money' => 0,
            'is_cheat' => 0,
            'inner_account' => 0,
            'pid' => 0,
            'create_time' => time(),
            'update_time' => time()
        ], $params);

        $user = UserModel::updateOrCreate(
            ['phone' => $userData['phone']],
            $userData
        );
        $this->log("创建/更新测试用户", $user->toArray());

        return [
            'user' => $user,
            'advertiser' => $advertiser
        ];
    }

    /**
     * 测试普通用户无上级代理的情况
     */
    public function testCollectPacketOneWithoutPid()
    {
        $this->log("\n开始测试: 普通用户无上级代理场景");
        
        // 准备测试数据
        $testData = $this->prepareTestData([
            'commission' => 100, // 30%分成
            'is_cheat' => 0,
            'reward_user' => 1,
            'reward_admin' => 1,
            'inner_account' => 0
        ]);

        // 设置请求参数
        $this->setRequestParams([
            'money' => 231250, // 1元
            'advertiser' => 'test_advertiser',
            'm_device_id' => 'test_device_123'
        ]);

        $this->log("设置系统配置");
        // 设置系统配置
        config([
            'app_config.money.max_money_user' => 0.5,
            'app_config.money.user' => 30,
            'app_config.money.cheat_user' => 0.05
        ]);

        try {
            $this->log("开始执行collectPacketOne方法");
            $result = $this->userService->collectPacketOne(['id' => $testData['user']->id]);
            
            $this->log("执行结果", $result);

            // 验证广告记录
            $ad = AdvertisementModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证广告记录", $ad ? $ad->toArray() : null);

            // 验证红包记录
            $redPacket = RedPacketModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证红包记录", $redPacket ? $redPacket->toArray() : null);


            // 验证用户余额
            $updatedUser = UserModel::where('id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证用户最终状态", $updatedUser->toArray());

            // 断言
            $this->assertNotNull($result);
            $this->assertArrayHasKey('my_money', $result);
            $this->assertArrayHasKey('user_money', $result);
            
        } catch (\Exception $e) {
            $this->log("测试执行异常", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 测试有上级代理的情况
     */
    public function testCollectPacketOneWithPid()
    {
        $this->log("\n开始测试: 用户有上级代理场景");

        // 创建上级代理用户
        $parentUser = UserModel::updateOrCreate(
            ['phone' => '***********'],
            [
                'phone' => '***********',
                'pwd' => md5('123456'),
                'invite_code' => 'TEST02',
                'identity' => 2,
                'commission' => 0,
                'admin_commission' => 0, // 40%代理分成
                'money' => 0,
                'is_cheat' => 0,
                'reward_user' => 1,
                'reward_admin' => 1,
                'inner_account' => 0,
                'create_time' => time(),
                'update_time' => time()
            ]
        );
        $this->log("创建/更新上级代理用户", $parentUser->toArray());

        // 准备下级用户测试数据
        $testData = $this->prepareTestData([
            'pid' => $parentUser->id,
            'commission' => 25,
            'user_commission' =>0, // 30%用户分成
            'is_cheat' => 1,
            'reward_user' => 1,
            'reward_admin' => 1,
            'inner_account' => 0
        ]);

        // 设置请求参数
        $this->setRequestParams([
            'money' => 100000, // 1元
            'advertiser' => 'test_advertiser',
            'm_device_id' => 'test_device_123'
        ]);

        $this->log("设置系统配置");
        // 设置系统配置
        config([
            'app_config.money.max_money_user' => 0.5,
            'app_config.money.max_money_agent' => 0.5,
            'app_config.money.admin' => 40,
            'app_config.money.user_invite' => 30,
            'app_config.money.cheat_user' => 0.05,
            'app_config.money.cheat_admin' => 0.05
        ]);

        try {
            $this->log("开始执行collectPacketOne方法");
            $result = $this->userService->collectPacketOne(['id' => $testData['user']->id]);
            
            $this->log("执行结果", $result);

            // 验证广告记录
            $ad = AdvertisementModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证广告记录", $ad ? $ad->toArray() : null);

            // 验证红包记录
            $redPackets = RedPacketModel::whereIn('user_id', [$testData['user']->id, $parentUser->id])
                ->orderByDesc('id')
                ->get();
            $this->log("验证红包记录", $redPackets ? $redPackets->toArray() : null);

            // 验证用户余额
            $updatedUser = UserModel::where('id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $updatedParent = UserModel::where('id', $parentUser->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证用户最终状态", [
                'user' => $updatedUser->toArray(),
                'parent' => $updatedParent->toArray()
            ]);

            // 断言
            $this->assertNotNull($result);
            $this->assertArrayHasKey('my_money', $result);
            $this->assertArrayHasKey('user_money', $result);

        } catch (\Exception $e) {
            $this->log("测试执行异常", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 测试作弊用户的情况
     */
    public function testCollectPacketOneWithCheatUser()
    {
        $this->log("\n开始测试: 作弊用户场景");

        // 准备测试数据
        $testData = $this->prepareTestData([
            'commission' => 30,
            'is_cheat' => 1, // 标记为作弊用户
            'inner_account' => 0
        ]);

        // 设置请求参数
        $this->setRequestParams([
            'money' => 100000, // 1元
            'advertiser' => 'test_advertiser',
            'm_device_id' => 'test_device_123'
        ]);

        $this->log("设置系统配置");
        // 设置系统配置
        config([
            'app_config.money.max_money_user' => 0.5,
            'app_config.money.user' => 30,
            'app_config.money.cheat_user' => 0.05
        ]);

        try {
            $this->log("开始执行collectPacketOne方法");
            $result = $this->userService->collectPacketOne(['id' => $testData['user']->id]);
            
            $this->log("执行结果", $result);

            // 验证广告记录
            $ad = AdvertisementModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证广告记录", $ad ? $ad->toArray() : null);


            // 验证红包记录
            $redPacket = RedPacketModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证红包记录", $redPacket ? $redPacket->toArray() : null);

            // 验证用户余额
            $updatedUser = UserModel::where('id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证用户最终状态", $updatedUser->toArray());


            // 断言
            $this->assertNotNull($result);
            $this->assertArrayHasKey('my_money', $result);
            $this->assertArrayHasKey('user_money', $result);
            $this->assertLessThanOrEqual(0.05, $result['my_money']); // 作弊用户奖励不超过0.05

        } catch (\Exception $e) {
            $this->log("测试执行异常", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 测试内部账号的情况
     */
    public function testCollectPacketOneWithInnerAccount()
    {
        $this->log("\n开始测试: 内部账号场景");

        // 准备测试数据
        $testData = $this->prepareTestData([
            'commission' => 30,
            'is_cheat' => 0,
            'inner_account' => 1 // 标记为内部账号
        ]);

        // 设置请求参数
        $this->setRequestParams([
            'money' => 100000, // 1元
            'advertiser' => 'test_advertiser',
            'm_device_id' => 'test_device_123'
        ]);

        $this->log("设置系统配置");
        // 设置系统配置
        config([
            'app_config.money.max_money_user' => 0.5,
            'app_config.money.user' => 30
        ]);

        try {
            $this->log("开始执行collectPacketOne方法");
            $result = $this->userService->collectPacketOne(['id' => $testData['user']->id]);
            
            $this->log("执行结果", $result);

            // 验证广告记录
            $ad = AdvertisementModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证广告记录", $ad ? $ad->toArray() : null);

            // 验证红包记录
            $redPacket = RedPacketModel::where('user_id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证红包记录", $redPacket ? $redPacket->toArray() : null);

            // 验证用户余额
            $updatedUser = UserModel::where('id', $testData['user']->id)
                ->orderByDesc('id')
                ->first();
            $this->log("验证用户最终状态", $updatedUser->toArray());

            // 断言
            $this->assertNotNull($result);
            $this->assertArrayHasKey('my_money', $result);
            $this->assertArrayHasKey('user_money', $result);

        } catch (\Exception $e) {
            $this->log("测试执行异常", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 设置请求参数
     */
    protected function setRequestParams($params)
    {
        $this->log("设置请求参数", $params);
        request()->merge($params);
    }
} 